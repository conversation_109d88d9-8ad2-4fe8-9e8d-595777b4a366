package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.RelationshipData;
import pt.kpmg.sph.policy.excel.data.ontologies.RelationshipSheetData;

@Mapper
public interface RelationshipDataMapper {

  RelationshipDataMapper INSTANCE = Mappers.getMapper(RelationshipDataMapper.class);

  RelationshipSheetData toRelationshipSheetData(RelationshipData data);

  List<RelationshipSheetData> toRelationshipSheetData(List<RelationshipData> data);

  @InheritInverseConfiguration
  RelationshipData toRelationshipData(RelationshipSheetData sheetData);

  @InheritInverseConfiguration
  List<RelationshipData> toRelationshipData(List<RelationshipSheetData> sheetData);
}
