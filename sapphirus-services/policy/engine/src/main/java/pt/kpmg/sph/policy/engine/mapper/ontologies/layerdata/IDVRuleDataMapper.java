package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.IDVRuleData;
import pt.kpmg.sph.policy.excel.data.ontologies.IDVRuleSheetData;

@Mapper
public interface IDVRuleDataMapper {

  IDVRuleDataMapper INSTANCE = Mappers.getMapper(IDVRuleDataMapper.class);

  IDVRuleSheetData toIDVRuleSheetData(IDVRuleData data);

  List<IDVRuleSheetData> toIDVRuleSheetData(List<IDVRuleData> data);

  @InheritInverseConfiguration
  IDVRuleData toIDVRuleData(IDVRuleSheetData sheetData);

  @InheritInverseConfiguration
  List<IDVRuleData> toIDVRuleData(List<IDVRuleSheetData> sheetData);
}
