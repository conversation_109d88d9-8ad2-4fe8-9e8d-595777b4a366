package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.ArtifactAttributeMappingData;
import pt.kpmg.sph.policy.excel.data.ontologies.ArtifactAttributeMappingSheetData;

@Mapper
public interface ArtifactAttributeMappingDataMapper {

  ArtifactAttributeMappingDataMapper INSTANCE = Mappers.getMapper(ArtifactAttributeMappingDataMapper.class);

  ArtifactAttributeMappingSheetData toArtifactAttributeMappingSheetData(
      ArtifactAttributeMappingData data);

  List<ArtifactAttributeMappingSheetData> toArtifactAttributeMappingSheetData(
      List<ArtifactAttributeMappingData> data);

  @InheritInverseConfiguration
  ArtifactAttributeMappingData toArtifactAttributeMappingData(
      ArtifactAttributeMappingSheetData sheetData);

  @InheritInverseConfiguration
  List<ArtifactAttributeMappingData> toArtifactAttributeMappingData(
      List<ArtifactAttributeMappingSheetData> sheetData);
}
