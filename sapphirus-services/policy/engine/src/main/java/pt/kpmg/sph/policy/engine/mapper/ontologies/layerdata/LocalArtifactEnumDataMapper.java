package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.LocalArtifactEnumData;
import pt.kpmg.sph.policy.excel.data.ontologies.LocalArtifactEnumSheetData;

@Mapper
public interface LocalArtifactEnumDataMapper {

  LocalArtifactEnumDataMapper INSTANCE = Mappers.getMapper(LocalArtifactEnumDataMapper.class);

  LocalArtifactEnumSheetData toLocalArtifactEnumSheetData(LocalArtifactEnumData data);

  List<LocalArtifactEnumSheetData> toLocalArtifactEnumSheetData(List<LocalArtifactEnumData> data);

  @InheritInverseConfiguration
  LocalArtifactEnumData toLocalArtifactEnumData(LocalArtifactEnumSheetData sheetData);

  @InheritInverseConfiguration
  List<LocalArtifactEnumData> toLocalArtifactEnumData(List<LocalArtifactEnumSheetData> sheetData);
}
