package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.ArtifactConfigurationData;
import pt.kpmg.sph.policy.excel.data.ontologies.ArtifactConfigurationSheetData;

@Mapper
public interface ArtifactConfigurationDataMapper {

  ArtifactConfigurationDataMapper INSTANCE = Mappers.getMapper(ArtifactConfigurationDataMapper.class);

  ArtifactConfigurationSheetData toArtifactConfigurationSheetData(ArtifactConfigurationData data);

  List<ArtifactConfigurationSheetData> toArtifactConfigurationSheetData(
      List<ArtifactConfigurationData> data);

  @InheritInverseConfiguration
  ArtifactConfigurationData toArtifactConfigurationData(
      ArtifactConfigurationSheetData sheetData);

  @InheritInverseConfiguration
  List<ArtifactConfigurationData> toArtifactConfigurationData(
      List<ArtifactConfigurationSheetData> sheetData);
}
