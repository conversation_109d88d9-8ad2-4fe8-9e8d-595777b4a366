package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.LocalArtifactSchemaData;
import pt.kpmg.sph.policy.excel.data.ontologies.LocalArtifactSchemaSheetData;

@Mapper
public interface LocalArtifactSchemaDataMapper {

  LocalArtifactSchemaDataMapper INSTANCE = Mappers.getMapper(LocalArtifactSchemaDataMapper.class);

  LocalArtifactSchemaSheetData toLocalArtifactSchemaSheetData(LocalArtifactSchemaData data);

  List<LocalArtifactSchemaSheetData> toLocalArtifactSchemaSheetData(
      List<LocalArtifactSchemaData> data);

  @InheritInverseConfiguration
  LocalArtifactSchemaData toLocalArtifactSchemaData(LocalArtifactSchemaSheetData sheetData);

  @InheritInverseConfiguration
  List<LocalArtifactSchemaData> toLocalArtifactSchemaData(
      List<LocalArtifactSchemaSheetData> sheetData);
}
