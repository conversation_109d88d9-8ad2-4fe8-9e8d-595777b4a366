package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.OntologyData;
import pt.kpmg.sph.policy.excel.data.ontologies.OntologySheetData;

@Mapper
public interface OntologyDataMapper {

  OntologyDataMapper INSTANCE = Mappers.getMapper(OntologyDataMapper.class);

  OntologySheetData toOntologySheetData(OntologyData data);

  List<OntologySheetData> toOntologySheetData(List<OntologyData> data);

  @InheritInverseConfiguration
  OntologyData toOntologyData(OntologySheetData sheetData);

  @InheritInverseConfiguration
  List<OntologyData> toOntologyData(List<OntologySheetData> sheetData);
}
