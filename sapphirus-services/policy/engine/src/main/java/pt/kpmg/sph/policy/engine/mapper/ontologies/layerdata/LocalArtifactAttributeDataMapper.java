package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.LocalArtifactAttributeData;
import pt.kpmg.sph.policy.excel.data.ontologies.LocalArtifactAttributeSheetData;

@Mapper
public interface LocalArtifactAttributeDataMapper {

  LocalArtifactAttributeDataMapper INSTANCE = Mappers.getMapper(LocalArtifactAttributeDataMapper.class);

  LocalArtifactAttributeSheetData toLocalArtifactAttributeSheetData(
      LocalArtifactAttributeData data);

  List<LocalArtifactAttributeSheetData> toLocalArtifactAttributeSheetData(
      List<LocalArtifactAttributeData> data);

  @InheritInverseConfiguration
  LocalArtifactAttributeData toLocalArtifactAttributeData(
      LocalArtifactAttributeSheetData sheetData);

  @InheritInverseConfiguration
  List<LocalArtifactAttributeData> toLocalArtifactAttributeData(
      List<LocalArtifactAttributeSheetData> sheetData);
}
