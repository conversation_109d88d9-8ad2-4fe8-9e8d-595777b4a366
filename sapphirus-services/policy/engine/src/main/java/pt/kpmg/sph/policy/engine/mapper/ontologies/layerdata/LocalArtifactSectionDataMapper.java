package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.LocalArtifactSectionData;
import pt.kpmg.sph.policy.excel.data.ontologies.LocalArtifactSectionSheetData;

@Mapper
public interface LocalArtifactSectionDataMapper {

  LocalArtifactSectionDataMapper INSTANCE = Mappers.getMapper(LocalArtifactSectionDataMapper.class);

  LocalArtifactSectionSheetData toLocalArtifactSectionSheetData(LocalArtifactSectionData data);

  List<LocalArtifactSectionSheetData> toLocalArtifactSectionSheetData(
      List<LocalArtifactSectionData> data);

  @InheritInverseConfiguration
  LocalArtifactSectionData toLocalArtifactSectionData(LocalArtifactSectionSheetData sheetData);

  @InheritInverseConfiguration
  List<LocalArtifactSectionData> toLocalArtifactSectionData(
      List<LocalArtifactSectionSheetData> sheetData);
}
