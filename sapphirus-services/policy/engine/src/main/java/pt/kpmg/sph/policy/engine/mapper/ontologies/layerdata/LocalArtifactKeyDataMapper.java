package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.LocalArtifactKeyData;
import pt.kpmg.sph.policy.excel.data.ontologies.LocalArtifactKeySheetData;

@Mapper
public interface LocalArtifactKeyDataMapper {

  LocalArtifactKeyDataMapper INSTANCE = Mappers.getMapper(LocalArtifactKeyDataMapper.class);

  LocalArtifactKeySheetData toLocalArtifactKeySheetData(LocalArtifactKeyData data);

  List<LocalArtifactKeySheetData> toLocalArtifactKeySheetData(List<LocalArtifactKeyData> data);

  @InheritInverseConfiguration
  LocalArtifactKeyData toLocalArtifactKeyData(LocalArtifactKeySheetData sheetData);

  @InheritInverseConfiguration
  List<LocalArtifactKeyData> toLocalArtifactKeyData(List<LocalArtifactKeySheetData> sheetData);
}
