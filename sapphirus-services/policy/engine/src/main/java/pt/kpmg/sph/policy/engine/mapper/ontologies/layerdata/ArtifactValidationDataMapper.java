package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.ArtifactValidationData;
import pt.kpmg.sph.policy.excel.data.ontologies.ArtifactValidationSheetData;

@Mapper
public interface ArtifactValidationDataMapper {

  ArtifactValidationDataMapper INSTANCE = Mappers.getMapper(ArtifactValidationDataMapper.class);

  ArtifactValidationSheetData toArtifactValidationSheetData(ArtifactValidationData data);

  List<ArtifactValidationSheetData> toArtifactValidationSheetData(List<ArtifactValidationData> data);

  @InheritInverseConfiguration
  ArtifactValidationData toArtifactValidationData(
      ArtifactValidationSheetData sheetData);

  @InheritInverseConfiguration
  List<ArtifactValidationData> toArtifactValidationData(
      List<ArtifactValidationSheetData> sheetData);
}
