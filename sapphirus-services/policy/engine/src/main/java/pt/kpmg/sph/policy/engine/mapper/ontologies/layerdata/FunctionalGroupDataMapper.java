package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.FunctionalGroupData;
import pt.kpmg.sph.policy.excel.data.ontologies.FunctionalGroupSheetData;

@Mapper
public interface FunctionalGroupDataMapper {

  FunctionalGroupDataMapper INSTANCE = Mappers.getMapper(FunctionalGroupDataMapper.class);

  FunctionalGroupSheetData toFunctionalGroupSheetData(FunctionalGroupData data);

  List<FunctionalGroupSheetData> toFunctionalGroupSheetData(List<FunctionalGroupData> data);

  @InheritInverseConfiguration
  FunctionalGroupData toFunctionalGroupData(FunctionalGroupSheetData sheetData);

  @InheritInverseConfiguration
  List<FunctionalGroupData> toFunctionalGroupData(List<FunctionalGroupSheetData> sheetData);
}
