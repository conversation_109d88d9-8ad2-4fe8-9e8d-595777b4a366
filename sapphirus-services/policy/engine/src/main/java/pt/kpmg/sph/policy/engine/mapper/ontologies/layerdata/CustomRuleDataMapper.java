package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.CustomRuleData;
import pt.kpmg.sph.policy.excel.data.ontologies.CustomRuleSheetData;

@Mapper
public interface CustomRuleDataMapper {

  CustomRuleDataMapper INSTANCE = Mappers.getMapper(CustomRuleDataMapper.class);

  CustomRuleSheetData toCustomRuleSheetData(CustomRuleData data);

  List<CustomRuleSheetData> toCustomRuleSheetData(List<CustomRuleData> data);

  @InheritInverseConfiguration
  CustomRuleData toCustomRuleData(CustomRuleSheetData sheetData);

  @InheritInverseConfiguration
  List<CustomRuleData> toCustomRuleData(List<CustomRuleSheetData> sheetData);
}
