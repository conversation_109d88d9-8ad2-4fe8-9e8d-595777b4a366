package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.ArtifactSectionMappingData;
import pt.kpmg.sph.policy.excel.data.ontologies.ArtifactSectionMappingSheetData;

@Mapper
public interface ArtifactSectionMappingDataMapper {

  ArtifactSectionMappingDataMapper INSTANCE = Mappers.getMapper(ArtifactSectionMappingDataMapper.class);

  ArtifactSectionMappingSheetData toArtifactSectionMappingSheetData(
      ArtifactSectionMappingData data);

  List<ArtifactSectionMappingSheetData> toArtifactSectionMappingSheetData(
      List<ArtifactSectionMappingData> data);

  @InheritInverseConfiguration
  ArtifactSectionMappingData toArtifactSectionMappingData(
      ArtifactSectionMappingSheetData sheetData);

  @InheritInverseConfiguration
  List<ArtifactSectionMappingData> toArtifactSectionMappingData(
      List<ArtifactSectionMappingSheetData> sheetData);
}
