package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.LocalArtifactEnumValueData;
import pt.kpmg.sph.policy.excel.data.ontologies.LocalArtifactEnumValueSheetData;

@Mapper
public interface LocalArtifactEnumValueDataMapper {

  LocalArtifactEnumValueDataMapper INSTANCE = Mappers.getMapper(LocalArtifactEnumValueDataMapper.class);

  LocalArtifactEnumValueSheetData toLocalArtifactEnumValueSheetData(
      LocalArtifactEnumValueData data);

  List<LocalArtifactEnumValueSheetData> toLocalArtifactEnumValueSheetData(
      List<LocalArtifactEnumValueData> data);

  @InheritInverseConfiguration
  LocalArtifactEnumValueData toLocalArtifactEnumValueData(
      LocalArtifactEnumValueSheetData sheetData);

  @InheritInverseConfiguration
  List<LocalArtifactEnumValueData> toLocalArtifactEnumValueData(
      List<LocalArtifactEnumValueSheetData> sheetData);
}
