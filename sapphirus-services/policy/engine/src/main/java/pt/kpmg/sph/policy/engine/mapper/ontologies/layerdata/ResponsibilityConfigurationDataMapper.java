package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.ResponsibilityConfigurationData;
import pt.kpmg.sph.policy.excel.data.ontologies.ResponsibilityConfigurationSheetData;

@Mapper
public interface ResponsibilityConfigurationDataMapper {

  ResponsibilityConfigurationDataMapper INSTANCE = Mappers.getMapper(ResponsibilityConfigurationDataMapper.class);

  ResponsibilityConfigurationSheetData toResponsibilityConfigurationSheetData(
      ResponsibilityConfigurationData data);

  List<ResponsibilityConfigurationSheetData> toResponsibilityConfigurationSheetData(
      List<ResponsibilityConfigurationData> data);

  @InheritInverseConfiguration
  ResponsibilityConfigurationData toResponsibilityConfigurationData(
      ResponsibilityConfigurationSheetData sheetData);

  @InheritInverseConfiguration
  List<ResponsibilityConfigurationData> toResponsibilityConfigurationData(
      List<ResponsibilityConfigurationSheetData> sheetData);
}
