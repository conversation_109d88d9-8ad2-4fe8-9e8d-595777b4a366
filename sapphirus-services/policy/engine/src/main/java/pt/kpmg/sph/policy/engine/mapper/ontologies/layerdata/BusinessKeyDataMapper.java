package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.BusinessKeyData;
import pt.kpmg.sph.policy.excel.data.ontologies.BusinessKeySheetData;

@Mapper
public interface BusinessKeyDataMapper {

  BusinessKeyDataMapper INSTANCE = Mappers.getMapper(BusinessKeyDataMapper.class);

  BusinessKeySheetData toBusinessKeySheetData(BusinessKeyData data);

  List<BusinessKeySheetData> toBusinessKeySheetData(List<BusinessKeyData> data);

  @InheritInverseConfiguration
  BusinessKeyData toBusinessKeyData(BusinessKeySheetData sheetData);

  @InheritInverseConfiguration
  List<BusinessKeyData> toBusinessKeyData(List<BusinessKeySheetData> sheetData);
}
