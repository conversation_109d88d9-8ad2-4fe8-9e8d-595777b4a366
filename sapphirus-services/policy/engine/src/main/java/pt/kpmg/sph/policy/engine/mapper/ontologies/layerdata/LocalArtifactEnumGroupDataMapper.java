package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.LocalArtifactEnumGroupData;
import pt.kpmg.sph.policy.excel.data.ontologies.LocalArtifactEnumGroupSheetData;

@Mapper
public interface LocalArtifactEnumGroupDataMapper {

  LocalArtifactEnumGroupDataMapper INSTANCE = Mappers.getMapper(LocalArtifactEnumGroupDataMapper.class);

  LocalArtifactEnumGroupSheetData toLocalArtifactEnumGroupSheetData(
      LocalArtifactEnumGroupData data);

  List<LocalArtifactEnumGroupSheetData> toLocalArtifactEnumGroupSheetData(
      List<LocalArtifactEnumGroupData> data);

  @InheritInverseConfiguration
  LocalArtifactEnumGroupData toLocalArtifactEnumGroupData(
      LocalArtifactEnumGroupSheetData sheetData);

  @InheritInverseConfiguration
  List<LocalArtifactEnumGroupData> toLocalArtifactEnumGroupData(
      List<LocalArtifactEnumGroupSheetData> sheetData);
}
