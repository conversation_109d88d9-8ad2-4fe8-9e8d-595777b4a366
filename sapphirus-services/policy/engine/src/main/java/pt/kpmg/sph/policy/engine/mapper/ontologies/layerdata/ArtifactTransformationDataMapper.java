package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.ArtifactTransformationData;
import pt.kpmg.sph.policy.excel.data.ontologies.ArtifactTransformationSheetData;

@Mapper
public interface ArtifactTransformationDataMapper {

  ArtifactTransformationDataMapper INSTANCE = Mappers.getMapper(ArtifactTransformationDataMapper.class);

  ArtifactTransformationSheetData toArtifactTransformationSheetData(
      ArtifactTransformationData data);

  List<ArtifactTransformationSheetData> toArtifactTransformationSheetData(
      List<ArtifactTransformationData> data);

  @InheritInverseConfiguration
  ArtifactTransformationData toArtifactTransformationData(
      ArtifactTransformationSheetData sheetData);

  @InheritInverseConfiguration
  List<ArtifactTransformationData> toArtifactTransformationData(
      List<ArtifactTransformationSheetData> sheetData);
}
