package pt.kpmg.sph.policy.engine.mapper.ontologies;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionIDVRule;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersionIDVRuleResult;
import pt.kpmg.sph.policy.engine.model.layer.IDVRuleData;
import pt.kpmg.sph.policy.engine.model.layer.enums.RuleType;

@Mapper
public interface RulesFileMapper {

  RulesFileMapper INSTANCE = Mappers.getMapper(RulesFileMapper.class);

  @Mapping(source = "idRule.entityType", target = "entityType")
  @Mapping(source = "idRule.entityCode", target = "entityCode")
  @Mapping(source = "idRule.attributeCode", target = "attributeCode")
  @Mapping(source = "ruleType", target = "ruleType")
  @Mapping(source = "ruleResult.code", target = "ruleCode")
  @Mapping(source = "ruleResult.name", target = "ruleName")
  @Mapping(source = "ruleResult.description", target = "ruleDescription")
  @Mapping(source = "condition", target = "conditionExpression")
  @Mapping(source = "ruleResult.required", target = "isRequired")
  @Mapping(source = "ruleResult.reasonCode", target = "reasonCode")
  @Mapping(source = "ruleResult.reason", target = "reasonDescription")
  @Mapping(source = "ruleResult.artifactPrecedence", target = "artifactPrecedence")
  IDVRuleData mapToIDVRuleData(
      OntologyVersionIDVRule idRule,
      OntologyVersionIDVRuleResult ruleResult,
      RuleType ruleType,
      String condition);

  @Mapping(source = "entityType", target = "entityType")
  @Mapping(source = "entityCode", target = "entityCode")
  @Mapping(source = "attributeCode", target = "attributeCode")
  OntologyVersionIDVRule mapToOntologyVersionIDVRule(IDVRuleData data);

  @Mapping(source = "ruleCode", target = "code")
  @Mapping(source = "ruleName", target = "name")
  @Mapping(source = "ruleDescription", target = "description")
  @Mapping(source = "isRequired", target = "required")
  @Mapping(source = "reasonCode", target = "reasonCode")
  @Mapping(source = "reasonDescription", target = "reason")
  @Mapping(source = "artifactPrecedence", target = "artifactPrecedence")
  OntologyVersionIDVRuleResult mapToOntologyVersionIDVRuleResult(IDVRuleData data);
}
