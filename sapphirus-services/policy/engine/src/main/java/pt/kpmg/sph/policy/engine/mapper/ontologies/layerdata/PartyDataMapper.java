package pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata;

import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import pt.kpmg.sph.policy.engine.model.layer.PartyData;
import pt.kpmg.sph.policy.excel.data.ontologies.PartySheetData;

@Mapper
public interface PartyDataMapper {

  PartyDataMapper INSTANCE = Mappers.getMapper(PartyDataMapper.class);

  PartySheetData toPartySheetData(PartyData data);

  List<PartySheetData> toPartySheetData(List<PartyData> data);

  @InheritInverseConfiguration
  PartyData toPartyData(PartySheetData sheetData);

  @InheritInverseConfiguration
  List<PartyData> toPartyData(List<PartySheetData> sheetData);
}
