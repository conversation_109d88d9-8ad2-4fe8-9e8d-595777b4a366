package test.policy.engine.model.layer;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;

import static org.assertj.core.api.Assertions.assertThat;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import lombok.SneakyThrows;

import pt.kpmg.sph.entities.model.policy.ontology.*;
import pt.kpmg.sph.policy.engine.model.layer.*;
import pt.kpmg.sph.policy.engine.model.layer.enums.*;
import pt.kpmg.sph.policy.engine.model.layer.enums.ArtifactRecycleType;
import pt.kpmg.sph.policy.engine.model.layer.enums.ArtifactUsageType;
import pt.kpmg.sph.policy.engine.model.layer.enums.ComparisonStrategy;
import pt.kpmg.sph.policy.engine.model.layer.enums.EntityType;
import pt.kpmg.sph.policy.engine.model.layer.enums.KeyStrength;
import pt.kpmg.sph.policy.engine.model.layer.enums.ResponsibilityLevel;
import pt.kpmg.sph.policy.engine.model.layer.enums.ValidationLevel;
import pt.kpmg.sph.policy.engine.model.layer.enums.ValidationMethod;
import pt.kpmg.sph.policy.engine.model.layer.enums.ValidationMoment;
import pt.kpmg.sph.policy.excel.config.ontologies.OntologyFile;
import pt.kpmg.sph.policy.excel.data.ontologies.OntologyFileData;
import pt.kpmg.sph.utils.mapper.MapperUtils;

class LayerDataUnitTest {

  private static final String BASE_PATH = "src/test/resources/";
  private LayerData layerData;

  @BeforeEach
  void setUp() {
    layerData = createBasicLayerData();
  }

  @Test
  @DisplayName("Should create OntologyVersion from complete LayerData")
  void shouldCreateOntologyVersionFromCompleteLayerData() {

    LayerData completeLayerData = getLayerDataFromJson("test-data/layer-data-complete-test.json");

    OntologyVersion result = completeLayerData.toConfig();

    assertThat(result).isNotNull();

    assertThat(result.getVersion()).isEqualTo(1);
    assertThat(result.getHashCode()).isEqualTo("abc123hash");

    assertThat(result.getFunctionalGroups()).hasSize(2);
    assertThat(result.getPartyTypes()).hasSize(2);
    assertThat(result.getRelationshipTypes()).hasSize(2);
    assertThat(result.getIdentificationRules()).hasSize(2);
    assertThat(result.getVerificationRules()).hasSize(1);
    assertThat(result.getCustomRules()).hasSize(1);
    assertThat(result.getResponsibilityConfigurations()).hasSize(1);
    assertThat(result.getArtifactSchemas()).hasSize(1);
    assertThat(result.getArtifactConfigurations()).hasSize(1);
  }

  @Test
  @DisplayName("Should process functional groups correctly")
  void shouldProcessFunctionalGroupsCorrectly() {

    List<FunctionalGroupData> functionalGroups = Arrays.asList(
        createFunctionalGroupData("KYC", "Know Your Customer"),
        createFunctionalGroupData("AML", "Anti Money Laundering")
    );
    layerData.setFunctionalGroups(functionalGroups);

    OntologyVersion result = layerData.toConfig();

    assertThat(result.getFunctionalGroups()).hasSize(2);

    OntologyVersionFunctionalGroup kyc = result.getFunctionalGroups().get(0);
    assertThat(kyc.getCode()).isEqualTo("KYC");
    assertThat(kyc.getName()).isEqualTo("Know Your Customer");

    OntologyVersionFunctionalGroup aml = result.getFunctionalGroups().get(1);
    assertThat(aml.getCode()).isEqualTo("AML");
    assertThat(aml.getName()).isEqualTo("Anti Money Laundering");
  }

  @Test
  @DisplayName("Should process parties with business keys and attributes correctly")
  void shouldProcessPartiesWithBusinessKeysAndAttributesCorrectly() {

    PartyData party = createPartyData("INDIVIDUAL", "Individual");
    BusinessKeyData businessKey = createBusinessKeyData("INDIVIDUAL",
        Arrays.asList("FIRST_NAME", "LAST_NAME"));
    AttributeData attribute = createAttributeData("INDIVIDUAL", "FIRST_NAME", EntityType.PARTY);

    layerData.setParties(Arrays.asList(party));
    layerData.setBusinessKeys(Arrays.asList(businessKey));
    layerData.setAttributes(Arrays.asList(attribute));

    OntologyVersion result = layerData.toConfig();

    assertThat(result.getPartyTypes()).hasSize(1);

    OntologyVersionPartyType processedParty = result.getPartyTypes().get(0);
    assertThat(processedParty.getCode()).isEqualTo("INDIVIDUAL");
    assertThat(processedParty.getName()).isEqualTo("Individual");
    assertThat(processedParty.getBusinessKeys()).hasSize(1);
    assertThat(processedParty.getAttributes()).hasSize(1);

    OntologyVersionBusinessKey processedBusinessKey = processedParty.getBusinessKeys().get(0);
    assertThat(processedBusinessKey.getAttributes()).containsExactly("FIRST_NAME", "LAST_NAME");

    OntologyVersionAttribute processedAttribute = processedParty.getAttributes().get(0);
    assertThat(processedAttribute.getCode()).isEqualTo("FIRST_NAME");
  }

  @Test
  @DisplayName("Should process relationships with configurations and attributes correctly")
  void shouldProcessRelationshipsWithConfigurationsAndAttributesCorrectly() {

    RelationshipData relationship1 = createRelationshipData("OWNS", "INDIVIDUAL", "COMPANY");
    RelationshipData relationship2 = createRelationshipData("OWNS", "COMPANY", "SUBSIDIARY");
    AttributeData relationshipAttribute = createAttributeData("OWNS", "OWNERSHIP_PERCENTAGE",
        EntityType.RELATIONSHIP);

    layerData.setRelationships(Arrays.asList(relationship1, relationship2));
    layerData.setAttributes(Arrays.asList(relationshipAttribute));

    OntologyVersion result = layerData.toConfig();

    assertThat(result.getRelationshipTypes()).hasSize(1);

    OntologyVersionRelationshipType processedRelationship = result.getRelationshipTypes().get(0);
    assertThat(processedRelationship.getCode()).isEqualTo("OWNS");
    assertThat(processedRelationship.getConfigurations()).hasSize(2);
    assertThat(processedRelationship.getAttributes()).hasSize(1);

    List<OntologyVersionRelationshipTypeConfiguration> configs = processedRelationship.getConfigurations();
    assertThat(configs.get(0).getParentPartyType()).isEqualTo("INDIVIDUAL");
    assertThat(configs.get(0).getChildPartyType()).isEqualTo("COMPANY");
    assertThat(configs.get(1).getParentPartyType()).isEqualTo("COMPANY");
    assertThat(configs.get(1).getChildPartyType()).isEqualTo("SUBSIDIARY");

    OntologyVersionAttribute processedAttribute = processedRelationship.getAttributes().get(0);
    assertThat(processedAttribute.getCode()).isEqualTo("OWNERSHIP_PERCENTAGE");
  }

  @Test
  @DisplayName("Should process identification rules correctly")
  void shouldProcessIdentificationRulesCorrectly() {

    IDVRuleData defaultRule = createIDVRuleData("INDIVIDUAL", "FIRST_NAME", RuleType.DEFAULT, null);
    IDVRuleData overrideRule = createIDVRuleData("INDIVIDUAL", "FIRST_NAME", RuleType.OVERRIDE,
        "length(FIRST_NAME) > 0");

    layerData.setIdRules(Arrays.asList(defaultRule, overrideRule));

    OntologyVersion result = layerData.toConfig();

    assertThat(result.getIdentificationRules()).hasSize(1);

    OntologyVersionIDVRule processedRule = result.getIdentificationRules().get(0);
    assertThat(processedRule.getEntityCode()).isEqualTo("INDIVIDUAL");
    assertThat(processedRule.getAttributeCode()).isEqualTo("FIRST_NAME");
    assertThat(processedRule.getDefaultValue()).isNotNull();
    assertThat(processedRule.getConditions()).hasSize(1);

    OntologyVersionIDVRuleCondition condition = processedRule.getConditions().get(0);
    assertThat(condition.getCondition()).isEqualTo("length(FIRST_NAME) > 0");
  }

  @Test
  @DisplayName("Should process verification rules correctly")
  void shouldProcessVerificationRulesCorrectly() {

    IDVRuleData vRule = createIDVRuleData("INDIVIDUAL", "FIRST_NAME", RuleType.DEFAULT, null);

    layerData.setVRules(Arrays.asList(vRule));

    OntologyVersion result = layerData.toConfig();

    assertThat(result.getVerificationRules()).hasSize(1);

    OntologyVersionIDVRule processedRule = result.getVerificationRules().get(0);
    assertThat(processedRule.getEntityCode()).isEqualTo("INDIVIDUAL");
    assertThat(processedRule.getAttributeCode()).isEqualTo("FIRST_NAME");
    assertThat(processedRule.getDefaultValue()).isNotNull();
  }

  @Test
  @DisplayName("Should process custom rules correctly")
  void shouldProcessCustomRulesCorrectly() {

    CustomRuleData customRule = createCustomRuleData("INDIVIDUAL", "*", "CUSTOM_RULE_1");

    layerData.setCustomRules(Arrays.asList(customRule));

    OntologyVersion result = layerData.toConfig();

    assertThat(result.getCustomRules()).hasSize(1);

    OntologyVersionPolicyRule processedRule = result.getCustomRules().get(0);
    assertThat(processedRule.getCode()).isEqualTo("CUSTOM_RULE_1");
  }

  @Test
  @DisplayName("Should process responsibility configurations correctly")
  void shouldProcessResponsibilityConfigurationsCorrectly() {

    ResponsibilityConfigurationData respConfig = createResponsibilityConfigurationData("INDIVIDUAL",
        "ADMIN");

    layerData.setResponsibilityConfiguration(Arrays.asList(respConfig));

    OntologyVersion result = layerData.toConfig();

    assertThat(result.getResponsibilityConfigurations()).hasSize(1);

    OntologyVersionResponsibilityConfiguration processedConfig = result.getResponsibilityConfigurations()
        .get(0);
    assertThat(processedConfig.getTargetEntityExpression()).isEqualTo("INDIVIDUAL");
    assertThat(processedConfig.getResponsibleEntityExpression()).isEqualTo("ADMIN");
  }

  @Test
  @DisplayName("Should process local artifact schemas with sections and attributes correctly")
  void shouldProcessLocalArtifactSchemasWithSectionsAndAttributesCorrectly() {

    LocalArtifactSchemaData schema = createLocalArtifactSchemaData("LOCAL_SCHEMA_1");
    LocalArtifactSectionData section = createLocalArtifactSectionData("LOCAL_SCHEMA_1",
        "SECTION_1");
    LocalArtifactAttributeData attribute = createLocalArtifactAttributeData("LOCAL_SCHEMA_1",
        "SECTION_1", "ATTR_1");
    LocalArtifactKeyData key = createLocalArtifactKeyData("LOCAL_SCHEMA_1", "SECTION_1", "ATTR_1");
    LocalArtifactEnumGroupData enumGroup = createLocalArtifactEnumGroupData("LOCAL_SCHEMA_1",
        "ENUM_GROUP_1");
    LocalArtifactEnumData enumData = createLocalArtifactEnumData("LOCAL_SCHEMA_1", "ENUM_GROUP_1",
        "ENUM_1");
    LocalArtifactEnumValueData enumValue = createLocalArtifactEnumValueData("LOCAL_SCHEMA_1",
        "ENUM_GROUP_1");

    layerData.setLocalArtifactSchemas(Arrays.asList(schema));
    layerData.setLocalArtifactSections(Arrays.asList(section));
    layerData.setLocalArtifactAttributes(Arrays.asList(attribute));
    layerData.setLocalArtifactKeys(Arrays.asList(key));
    layerData.setLocalArtifactEnumGroups(Arrays.asList(enumGroup));
    layerData.setLocalArtifactEnums(Arrays.asList(enumData));
    layerData.setLocalArtifactEnumValues(Arrays.asList(enumValue));

    OntologyVersion result = layerData.toConfig();

    assertThat(result.getArtifactSchemas()).hasSize(1);

    OntologyVersionArtifactSchema processedSchema = result.getArtifactSchemas().get(0);
    assertThat(processedSchema.getArtifactSchemaCode()).isEqualTo("LOCAL_SCHEMA_1");
    assertThat(processedSchema.getSections()).hasSize(1);
    assertThat(processedSchema.getKeys()).hasSize(1);
    assertThat(processedSchema.getEnums()).hasSize(1);
  }

  @Test
  @DisplayName("Should process artifact configurations correctly")
  void shouldProcessArtifactConfigurationsCorrectly() {

    ArtifactConfigurationData artifactConfig = createArtifactConfigurationData("ARTIFACT_SCHEMA_1");
    ArtifactValidationData validation = createArtifactValidationData("ARTIFACT_SCHEMA_1");
    ArtifactTransformationData transformation = createArtifactTransformationData(
        "ARTIFACT_SCHEMA_1");
    ArtifactSectionMappingData sectionMapping = createArtifactSectionMappingData(
        "ARTIFACT_SCHEMA_1");
    ArtifactAttributeMappingData attributeMapping = createArtifactAttributeMappingData(
        "ARTIFACT_SCHEMA_1");

    layerData.setArtifactConfigurations(Arrays.asList(artifactConfig));
    layerData.setArtifactValidations(Arrays.asList(validation));
    layerData.setArtifactTransformations(Arrays.asList(transformation));
    layerData.setArtifactSectionMappings(Arrays.asList(sectionMapping));
    layerData.setArtifactAttributeMappings(Arrays.asList(attributeMapping));

    OntologyVersion result = layerData.toConfig();

    assertThat(result.getArtifactConfigurations()).hasSize(1);

    OntologyVersionArtifactConfiguration processedConfig = result.getArtifactConfigurations()
        .get(0);
    assertThat(processedConfig.getArtifactSchemaCode()).isEqualTo("ARTIFACT_SCHEMA_1");
  }

  @Test
  @DisplayName("Should handle null ontologyVersion gracefully")
  void shouldHandleNullOntologyVersionGracefully() {

    layerData.setOntologyVersion(null);

    OntologyVersion result = layerData.toConfig();

    assertThat(result).isNotNull();
    assertThat(result.getFunctionalGroups()).isNull();
    assertThat(result.getPartyTypes()).isNull();
  }

  @Test
  @DisplayName("Should handle empty collections gracefully")
  void shouldHandleEmptyCollectionsGracefully() {

    layerData.setFunctionalGroups(new ArrayList<>());
    layerData.setParties(new ArrayList<>());
    layerData.setRelationships(new ArrayList<>());

    OntologyVersion result = layerData.toConfig();

    assertThat(result.getFunctionalGroups()).isEmpty();
    assertThat(result.getPartyTypes()).isEmpty();
    assertThat(result.getRelationshipTypes()).isEmpty();
  }

  @Test
  @DisplayName("Should test createRules static method with default rule")
  void shouldTestCreateRulesStaticMethodWithDefaultRule() {

    IDVRuleData defaultRule = createIDVRuleData("INDIVIDUAL", "FIRST_NAME", RuleType.DEFAULT, null);
    List<String> appliedRules = new ArrayList<>();

    LayerData.IDVRuleConsumer consumer = (entityType, entityCode, attributeCode, isDefault, condition, ruleResult) -> {
      appliedRules.add(entityCode + ":" + attributeCode + ":" + isDefault);
    };

    LayerData.createRules(defaultRule, consumer);

    assertThat(appliedRules).hasSize(1);
    assertThat(appliedRules.get(0)).isEqualTo("INDIVIDUAL:FIRST_NAME:true");
  }

  @Test
  @DisplayName("Should test createRules static method with conditional rule")
  void shouldTestCreateRulesStaticMethodWithConditionalRule() {

    IDVRuleData conditionalRule = createIDVRuleData("INDIVIDUAL", "FIRST_NAME", RuleType.OVERRIDE,
        "length(FIRST_NAME) > 0");
    List<String> appliedRules = new ArrayList<>();

    LayerData.IDVRuleConsumer consumer = (entityType, entityCode, attributeCode, isDefault, condition, ruleResult) -> {
      appliedRules.add(entityCode + ":" + attributeCode + ":" + isDefault + ":" + condition);
    };

    LayerData.createRules(conditionalRule, consumer);

    assertThat(appliedRules).hasSize(1);
    assertThat(appliedRules.get(0)).isEqualTo("INDIVIDUAL:FIRST_NAME:false:length(FIRST_NAME) > 0");
  }

  private LayerData createBasicLayerData() {
    return new LayerData(
        createOntologyData(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>(),
        new ArrayList<>()
    );
  }

  @SneakyThrows
  private LayerData getLayerDataFromJson(String fileName) {
    return MapperUtils.getObjectMapper().readValue(getFileAsString(fileName), LayerData.class);
  }

  @SneakyThrows
  private String getFileAsString(String fileName) {
    ClassLoader classLoader = getClass().getClassLoader();
    try (var inputStream = classLoader.getResourceAsStream(fileName)) {
      if (inputStream == null) {
        throw new IllegalArgumentException("File not found: " + fileName);
      }
      return new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
    }
  }

  private OntologyData createOntologyData() {
    OntologyData ontologyData = new OntologyData();
    ontologyData.setOntologyCode("TEST_ONTOLOGY");
    ontologyData.setOntologyName("Test Ontology");
    ontologyData.setOntologyVersion(1);
    ontologyData.setLastUpdatedOn(Instant.now());
    ontologyData.setHash("test-hash");
    return ontologyData;
  }

  private FunctionalGroupData createFunctionalGroupData(String code, String name) {
    FunctionalGroupData functionalGroup = new FunctionalGroupData();
    functionalGroup.setFunctionalGroupCode(code);
    functionalGroup.setFunctionalGroupName(name);
    functionalGroup.setFunctionalGroupDescription(name + " processes and validations");
    functionalGroup.setLayerAction(LayerAction.NEW);
    functionalGroup.setAllowEdit(true);
    functionalGroup.setAllowDisable(false);
    return functionalGroup;
  }

  private PartyData createPartyData(String code, String name) {
    PartyData party = new PartyData();
    party.setPartyCode(code);
    party.setPartyName(name);
    party.setPartyDescription(name + " entity");
    party.setAllowMultipleArtifactSources(true);
    party.setArtifactPrecedence(Arrays.asList("ID_DOCUMENT", "PASSPORT"));
    party.setLayerAction(LayerAction.NEW);
    party.setAllowEdit(true);
    party.setAllowDisable(false);
    party.setAllowAddAttributes(true);
    party.setAllowAddBusinessKeys(true);
    return party;
  }

  private BusinessKeyData createBusinessKeyData(String partyCode, List<String> attributeCodes) {
    BusinessKeyData businessKey = new BusinessKeyData();
    businessKey.setPartyCode(partyCode);
    businessKey.setAttributeCodes(attributeCodes);
    businessKey.setOrder(1);
    businessKey.setOtherAttributesToValidate(Arrays.asList("BIRTH_DATE"));
    businessKey.setKeyName("Primary Key");
    businessKey.setKeyDescription("Primary business key for " + partyCode);
    businessKey.setLayerAction(LayerAction.NEW);
    businessKey.setAllowEdit(true);
    businessKey.setAllowDisable(false);
    businessKey.setKeyStrength(KeyStrength.STRONG);
    return businessKey;
  }

  private AttributeData createAttributeData(String entityCode, String attributeCode,
      EntityType entityType) {
    AttributeData attribute = new AttributeData();
    attribute.setEntityCode(entityCode);
    attribute.setAttributeCode(attributeCode);
    attribute.setAttributeName(attributeCode.replace("_", " "));
    attribute.setAttributeDescription("Description for " + attributeCode);
    attribute.setEntityType(entityType);
    attribute.setAttributeType(AttributeType.STRING);
    attribute.setComparisonStrategy(ComparisonStrategy.DEFAULT);
    attribute.setComparisonClassName("StringComparator");
    attribute.setLayerAction(LayerAction.NEW);
    attribute.setAllowEdit(true);
    attribute.setAllowDisable(false);
    attribute.setAllowEditIDRules(true);
    attribute.setAllowEditVRules(true);
    return attribute;
  }

  private RelationshipData createRelationshipData(String code, String parentPartyType,
      String childPartyType) {
    RelationshipData relationship = new RelationshipData();
    relationship.setRelationshipCode(code);
    relationship.setRelationshipName(code + " relationship");
    relationship.setRelationshipDescription(code + " relationship description");
    relationship.setParentPartyType(parentPartyType);
    relationship.setChildPartyType(childPartyType);
    relationship.setAllowMultipleArtifactSources(true);
    relationship.setArtifactPrecedence(Arrays.asList("RELATIONSHIP_DOCUMENT"));
    relationship.setLayerAction(LayerAction.NEW);
    relationship.setAllowEdit(true);
    relationship.setAllowDisable(false);
    relationship.setAllowAddAttributes(true);
    return relationship;
  }

  private IDVRuleData createIDVRuleData(String entityCode, String attributeCode, RuleType ruleType,
      String conditionExpression) {
    IDVRuleData idvRule = new IDVRuleData();
    idvRule.setEntityCode(entityCode);
    idvRule.setAttributeCode(attributeCode);
    idvRule.setConditionExpression(conditionExpression);
    idvRule.setRuleCode("RULE_" + attributeCode);
    idvRule.setRuleName("Rule for " + attributeCode);
    idvRule.setRuleDescription("Rule description for " + attributeCode);
    idvRule.setIsRequired(true);
    idvRule.setReasonCode("REASON_" + attributeCode);
    idvRule.setReasonDescription("Reason for " + attributeCode);
    idvRule.setArtifactPrecedence(Arrays.asList("ID_DOCUMENT"));
    idvRule.setLayerAction(LayerAction.NEW);
    idvRule.setEntityType(EntityType.PARTY);
    idvRule.setRuleType(ruleType);
    return idvRule;
  }

  private CustomRuleData createCustomRuleData(String entityCode, String attributeCodePattern,
      String ruleCode) {
    CustomRuleData customRule = new CustomRuleData();
    customRule.setEntityCode(entityCode);
    customRule.setAttributeCodePattern(attributeCodePattern);
    customRule.setRuleCode(ruleCode);
    customRule.setRuleName("Custom Rule");
    customRule.setRuleDescription("Custom validation rule");
    customRule.setApplyWhen("always");
    customRule.setRuleExpression("true");
    customRule.setReasonCode("CUSTOM_FAIL");
    customRule.setReasonDescription("Custom rule failed");
    customRule.setLayerAction(LayerAction.NEW);
    customRule.setAllowEdit(true);
    customRule.setAllowDisable(false);
    customRule.setEntityType(EntityType.PARTY);
    customRule.setRuleLevel(RuleLevel.ATTRIBUTE);
    customRule.setRuleResult(RuleResult.PASS);
    return customRule;
  }

  private ResponsibilityConfigurationData createResponsibilityConfigurationData(String targetEntity,
      String responsibleEntity) {
    ResponsibilityConfigurationData respConfig = new ResponsibilityConfigurationData();
    respConfig.setTargetEntityExpression(targetEntity);
    respConfig.setResponsibleEntityExpression(responsibleEntity);
    respConfig.setAttributeNamePattern("*");
    respConfig.setRuleCode("RESP_RULE_1");
    respConfig.setDelegationCode("DELEGATION_1");
    respConfig.setDelegationName("Delegation 1");
    respConfig.setDelegationDescription("Responsibility delegation");
    respConfig.setLayerAction(LayerAction.NEW);
    respConfig.setAllowEdit(true);
    respConfig.setAllowDisable(false);
    respConfig.setLevel(ResponsibilityLevel.PARTY);
    return respConfig;
  }

  private LocalArtifactSchemaData createLocalArtifactSchemaData(String schemaCode) {
    LocalArtifactSchemaData schema = new LocalArtifactSchemaData();
    schema.setArtifactSchemaCode(schemaCode);
    schema.setArtifactSchemaName("Local Schema");
    schema.setArtifactSchemaDescription("Local artifact schema for testing");
    schema.setIsIndexable(true);
    schema.setTags(Arrays.asList("tag1", "tag2"));
    schema.setLayerAction(LayerAction.NEW);
    schema.setAllowEdit(true);
    schema.setAllowDisable(false);
    schema.setAllowAddSections(true);
    return schema;
  }

  private LocalArtifactSectionData createLocalArtifactSectionData(String schemaCode,
      String sectionCode) {
    LocalArtifactSectionData section = new LocalArtifactSectionData();
    section.setArtifactSchemaCode(schemaCode);
    section.setSectionCode(sectionCode);
    section.setSectionName("Section");
    section.setSectionDescription("Local artifact section for testing");
    section.setIsRepeatable(true);
    section.setIsMandatory(false);
    section.setLayerAction(LayerAction.NEW);
    section.setAllowEdit(true);
    section.setAllowDisable(false);
    section.setAllowAddAttributes(true);
    return section;
  }

  private LocalArtifactAttributeData createLocalArtifactAttributeData(String schemaCode,
      String sectionCode, String attributeCode) {
    LocalArtifactAttributeData attribute = new LocalArtifactAttributeData();
    attribute.setArtifactSchemaCode(schemaCode);
    attribute.setSectionCode(sectionCode);
    attribute.setAttributeCode(attributeCode);
    attribute.setAttributeName("Attribute");
    attribute.setAttributeDescription("Local artifact attribute for testing");
    attribute.setPattern(".*");
    attribute.setLayerAction(LayerAction.NEW);
    attribute.setAllowEdit(true);
    attribute.setAllowDisable(false);
    attribute.setAllowAddKeys(true);
    attribute.setAllowAddEnums(true);
    attribute.setAttributeType(AttributeType.STRING);
    return attribute;
  }

  private LocalArtifactKeyData createLocalArtifactKeyData(String schemaCode, String sectionCode,
      String attributeCode) {
    LocalArtifactKeyData key = new LocalArtifactKeyData();
    key.setArtifactSchemaCode(schemaCode);
    key.setSectionCode(sectionCode);
    key.setAttributeCode(attributeCode);
    key.setKeyGroup("GROUP_1");
    key.setKeyName("Key");
    key.setKeyDescription("Local artifact key for testing");
    key.setLayerAction(LayerAction.NEW);
    key.setAllowEdit(true);
    key.setAllowDisable(false);
    return key;
  }

  private LocalArtifactEnumGroupData createLocalArtifactEnumGroupData(String schemaCode,
      String enumGroupCode) {
    LocalArtifactEnumGroupData enumGroup = new LocalArtifactEnumGroupData();
    enumGroup.setArtifactSchemaCode(schemaCode);
    enumGroup.setEnumGroupCode(enumGroupCode);
    enumGroup.setEnumGroupName("Enum Group");
    enumGroup.setEnumGroupDescription("Local artifact enum group for testing");
    enumGroup.setOverrideGlobalEnumGroup(false);
    enumGroup.setLayerAction(LayerAction.NEW);
    enumGroup.setAllowEdit(true);
    enumGroup.setAllowDisable(false);
    enumGroup.setAllowAddEnums(true);
    return enumGroup;
  }

  private LocalArtifactEnumData createLocalArtifactEnumData(String schemaCode, String enumGroupCode,
      String enumCode) {
    LocalArtifactEnumData enumData = new LocalArtifactEnumData();
    enumData.setArtifactSchemaCode(schemaCode);
    enumData.setEnumGroupCode(enumGroupCode);
    enumData.setEnumCode(enumCode);
    enumData.setEnumName("Enum");
    enumData.setEnumDescription("Local artifact enum for testing");
    enumData.setLayerAction(LayerAction.NEW);
    enumData.setAllowEdit(true);
    enumData.setAllowDisable(false);
    enumData.setAllowAddEnumValues(true);
    enumData.setEnumColumn(EnumColumn.COL1);
    return enumData;
  }

  private LocalArtifactEnumValueData createLocalArtifactEnumValueData(String schemaCode,
      String enumGroupCode) {
    LocalArtifactEnumValueData enumValue = new LocalArtifactEnumValueData();
    enumValue.setArtifactSchemaCode(schemaCode);
    enumValue.setEnumGroupCode(enumGroupCode);
    enumValue.setCol1("Value1");
    enumValue.setCol2("Value2");
    enumValue.setCol3("Value3");
    enumValue.setCol4("Value4");
    enumValue.setCol5("Value5");
    enumValue.setCol6("Value6");
    enumValue.setCol7("Value7");
    enumValue.setCol8("Value8");
    enumValue.setCol9("Value9");
    enumValue.setCol10("Value10");
    enumValue.setOtherValues(Arrays.asList("Other1", "Other2"));
    enumValue.setLayerAction(LayerAction.NEW);
    enumValue.setAllowEdit(true);
    enumValue.setAllowDisable(false);
    return enumValue;
  }

  private ArtifactConfigurationData createArtifactConfigurationData(String schemaCode) {
    ArtifactConfigurationData artifactConfig = new ArtifactConfigurationData();
    artifactConfig.setArtifactSchemaCode(schemaCode);
    artifactConfig.setArtifactSchemaVersion(1);
    artifactConfig.setFunctionalGroupCodes(Arrays.asList("KYC", "AML"));
    artifactConfig.setIsRecyclable(true);
    artifactConfig.setConsiderForMismatchValues(true);
    artifactConfig.setConsiderForMissingValues(false);
    artifactConfig.setExpirationDateExpression("now() + 365 days");
    artifactConfig.setLayerAction(LayerAction.NEW);
    artifactConfig.setAllowEdit(true);
    artifactConfig.setAllowDisable(false);
    artifactConfig.setAllowAddSectionMappings(true);
    artifactConfig.setUsageType(ArtifactUsageType.GLOBAL);
    artifactConfig.setRecycleType(ArtifactRecycleType.AUTOMATIC);
    return artifactConfig;
  }

  private ArtifactValidationData createArtifactValidationData(String schemaCode) {
    ArtifactValidationData validation = new ArtifactValidationData();
    validation.setArtifactSchemaCode(schemaCode);
    validation.setSectionCodePattern("SECTION_*");
    validation.setAttributeCodePattern("ATTR_*");
    validation.setValidationName("Validation");
    validation.setValidationDescription("Artifact validation rule for testing");
    validation.setParameters(Arrays.asList("param1", "param2"));
    validation.setValidateWhen("always");
    validation.setReasonCode("VALIDATION_FAIL");
    validation.setReasonDescription("Validation failed");
    validation.setLayerAction(LayerAction.NEW);
    validation.setAllowEdit(true);
    validation.setAllowDisable(false);
    validation.setValidationLevel(ValidationLevel.ATTRIBUTE);
    validation.setValidationMethod(ValidationMethod.REGEX);
    validation.setApplyValidation(ValidationMoment.BOTH);
    return validation;
  }

  private ArtifactTransformationData createArtifactTransformationData(String schemaCode) {
    ArtifactTransformationData transformation = new ArtifactTransformationData();
    transformation.setArtifactSchemaCode(schemaCode);
    transformation.setSectionCodePattern("SECTION_*");
    transformation.setAttributeCodePattern("ATTR_*");
    transformation.setAttributeValuePattern(".*");
    transformation.setTransformedValueExpression("toUpperCase(value)");
    transformation.setTransformWhen("always");
    transformation.setTransformationName("Transformation");
    transformation.setTransformationDescription("Artifact transformation rule for testing");
    transformation.setLayerAction(LayerAction.NEW);
    transformation.setAllowEdit(true);
    transformation.setAllowDisable(false);
    return transformation;
  }

  private ArtifactSectionMappingData createArtifactSectionMappingData(String schemaCode) {
    ArtifactSectionMappingData sectionMapping = new ArtifactSectionMappingData();
    sectionMapping.setArtifactSchemaCode(schemaCode);
    sectionMapping.setSectionCodePattern("SECTION_*");
    sectionMapping.setEntityIdentifier("INDIVIDUAL");
    sectionMapping.setMappingName("Section Mapping");
    sectionMapping.setMappingDescription("Artifact section mapping for testing");
    sectionMapping.setEntityType(EntityType.PARTY);
    sectionMapping.setEntityCodeExpression("INDIVIDUAL");
    sectionMapping.setParentIdentifier("ROOT");
    sectionMapping.setChildIdentifier("CHILD");
    sectionMapping.setMapWhen("always");
    sectionMapping.setIsIndexableForRecycling(true);
    sectionMapping.setLayerAction(LayerAction.NEW);
    sectionMapping.setAllowEdit(true);
    sectionMapping.setAllowDisable(false);
    sectionMapping.setAllowAddAttributeMappings(true);
    return sectionMapping;
  }

  private ArtifactAttributeMappingData createArtifactAttributeMappingData(String schemaCode) {
    ArtifactAttributeMappingData attributeMapping = new ArtifactAttributeMappingData();
    attributeMapping.setArtifactSchemaCode(schemaCode);
    attributeMapping.setSectionCode("SECTION_1");
    attributeMapping.setAttributeCodeExpression("ATTR_*");
    attributeMapping.setEntityIdentifier("INDIVIDUAL");
    attributeMapping.setMappingName("Attribute Mapping");
    attributeMapping.setMappingDescription("Artifact attribute mapping for testing");
    attributeMapping.setEntityCodePattern("INDIVIDUAL");
    attributeMapping.setDestinationAttributeCode("FIRST_NAME");
    attributeMapping.setMapWhen("always");
    attributeMapping.setLayerAction(LayerAction.NEW);
    attributeMapping.setAllowEdit(true);
    attributeMapping.setAllowDisable(false);
    attributeMapping.setAllowAddAttributeValidations(true);
    attributeMapping.setAllowAddAttributeTransformations(true);
    attributeMapping.setEntityType(EntityType.PARTY);
    return attributeMapping;
  }

  @Test
  void testFileManagement() throws IOException {
    byte[] fileFromResources = getExampleByteArrayFromResources("ontology.xlsx");

    OntologyFile ontologyFile = new OntologyFile();

    // Transform into object of DATA
    OntologyFileData ontologyFileDataFromExcel = ontologyFile.fromExcel(fileFromResources);
    assertThat(ontologyFileDataFromExcel).isNotNull();

    assertThat(ontologyFileDataFromExcel.getBusinessKeys()).isNotEmpty();
    assertThat(ontologyFileDataFromExcel.getBusinessKeys().get(0).getKeyStrength())
        .isEqualTo(pt.kpmg.sph.policy.excel.data.ontologies.KeyStrength.STRONG);

    // Transform object from DATA into object of ENTITIES
    OntologyVersion ontologyVersion = ontologyFileDataFromExcel.toConfig();
    assertThat(ontologyVersion).isNotNull();

    // Transform object from ENTITIES back into object of DATA
    OntologyFileData ontologyFileDataFromOntology =
        new OntologyFileData(ontologyVersion, "KYC_DD_ONTOLOGY", "KPMG KYC Due Diligence");
    assertThat(ontologyFileDataFromOntology).isNotNull();

    assertThat(ontologyFileDataFromOntology.getBusinessKeys()).isNotEmpty();
    assertThat(ontologyFileDataFromOntology.getBusinessKeys().get(0).getKeyStrength())
        .isEqualTo(pt.kpmg.sph.policy.excel.data.ontologies.KeyStrength.STRONG);

    // Test: Are the first DATA object and re-converted DATA object equals?
    assertThat(ontologyFileDataFromExcel).isEqualTo(ontologyFileDataFromOntology);

    LayerData layerData = new LayerData(ontologyVersion, "KYC_DD_ONTOLOGY", "KPMG KYC Due Diligence");

    // Transform object from DATA into the excel (byte[])
    byte[] fileFromConverted = ontologyFile.toExcel(ontologyFileDataFromOntology);
    assertThat(fileFromConverted).isNotNull();

    // Transform from excel INTO object from DATA
    OntologyFileData reConversionResult = ontologyFile.fromExcel(fileFromConverted);
    assertThat(reConversionResult).isNotNull();

    // Transform from DATA object to CONFIG
    OntologyVersion reconvertedOntologyVersion = reConversionResult.toConfig();
    assertThat(reconvertedOntologyVersion).isNotNull();

    // Test: After transform into Excel and back to the object of CONFIG,
    // is it still the same from the beginning?
    assertThat(ontologyVersion).isEqualTo(reconvertedOntologyVersion);
  }

  protected File getExampleFileFromResources(String fileName) {
    return new File(BASE_PATH.concat(fileName));
  }

  protected byte[] getExampleByteArrayFromResources(String fileName) throws IOException {
    return Files.readAllBytes(getExampleFileFromResources(fileName).toPath());
  }
}
