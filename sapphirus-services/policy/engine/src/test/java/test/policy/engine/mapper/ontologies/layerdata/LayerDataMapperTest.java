package test.policy.engine.mapper.ontologies.layerdata;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.assertj.core.api.Assertions.assertThat;

import java.io.File;
import java.nio.charset.StandardCharsets;
import org.apache.commons.io.FileUtils;
import lombok.SneakyThrows;

import pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata.ArtifactAttributeMappingDataMapper;
import pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata.ArtifactSectionMappingDataMapper;
import pt.kpmg.sph.policy.engine.model.layer.*;
import pt.kpmg.sph.policy.engine.mapper.ontologies.layerdata.LayerDataMapper;
import pt.kpmg.sph.policy.excel.data.ontologies.*;
import pt.kpmg.sph.utils.mapper.MapperUtils;

class LayerDataMapperTest {

    private static final String BASE_PATH = "src/test/resources/";

    @Test
    @DisplayName("Should map LayerData to OntologyFileData correctly")
    void shouldMapLayerDataToOntologyFileData() {
        // Given
        LayerData layerData = getLayerDataFromJson("test-data/layer-data-sample.json");
        
        // When
        OntologyFileData result = LayerDataMapper.INSTANCE.mapToOntologyFileData(layerData);
        
        // Then
        assertThat(result).isNotNull();
        
        // Verify all main lists
        assertThat(result.getOntologyVersion()).hasSize(1);
        assertThat(result.getFunctionalGroups()).hasSize(1);
        assertThat(result.getParties()).hasSize(1);
        assertThat(result.getRelationships()).hasSize(1);
        assertThat(result.getAttributes()).hasSize(1);
        assertThat(result.getBusinessKeys()).hasSize(1);
        assertThat(result.getIdRules()).hasSize(1);
        assertThat(result.getVRules()).hasSize(1);
        assertThat(result.getCustomRules()).hasSize(1);
        assertThat(result.getResponsibilityConfiguration()).hasSize(1);
        assertThat(result.getLocalArtifactSchemas()).hasSize(1);
        assertThat(result.getLocalArtifactSections()).hasSize(1);
        assertThat(result.getLocalArtifactAttributes()).hasSize(1);
        assertThat(result.getLocalArtifactKeys()).hasSize(1);
        assertThat(result.getLocalArtifactEnumGroups()).hasSize(1);
        assertThat(result.getLocalArtifactEnums()).hasSize(1);
        assertThat(result.getLocalArtifactEnumValues()).hasSize(1);
        assertThat(result.getArtifactConfigurations()).hasSize(1);
        assertThat(result.getArtifactValidations()).hasSize(1);
        assertThat(result.getArtifactTransformations()).hasSize(1);
        assertThat(result.getArtifactSectionMappings()).hasSize(1);
        assertThat(result.getArtifactAttributeMappings()).hasSize(1);
    }

    @Test
    @DisplayName("Should map OntologyData correctly")
    void shouldMapOntologyDataCorrectly() {
        // Given
        LayerData layerData = getLayerDataFromJson("test-data/layer-data-sample.json");
        
        // When
        OntologyFileData result = LayerDataMapper.INSTANCE.mapToOntologyFileData(layerData);
        
        // Then
        OntologySheetData ontologySheet = result.getOntologyVersion().get(0);
        OntologyData originalOntology = layerData.getOntologyVersion();
        
        assertThat(ontologySheet.getOntologyCode()).isEqualTo(originalOntology.getOntologyCode());
        assertThat(ontologySheet.getOntologyName()).isEqualTo(originalOntology.getOntologyName());
        assertThat(ontologySheet.getOntologyVersion()).isEqualTo(originalOntology.getOntologyVersion());
        assertThat(ontologySheet.getLastUpdatedOn()).isEqualTo(originalOntology.getLastUpdatedOn());
        assertThat(ontologySheet.getHash()).isEqualTo(originalOntology.getHash());
    }

    @Test
    @DisplayName("Should map FunctionalGroupData correctly")
    void shouldMapFunctionalGroupDataCorrectly() {
        // Given
        LayerData layerData = getLayerDataFromJson("test-data/layer-data-sample.json");
        
        // When
        OntologyFileData result = LayerDataMapper.INSTANCE.mapToOntologyFileData(layerData);
        
        // Then
        FunctionalGroupSheetData functionalGroupSheet = result.getFunctionalGroups().get(0);
        FunctionalGroupData originalFunctionalGroup = layerData.getFunctionalGroups().get(0);
        
        assertThat(functionalGroupSheet.getFunctionalGroupCode()).isEqualTo(originalFunctionalGroup.getFunctionalGroupCode());
        assertThat(functionalGroupSheet.getFunctionalGroupName()).isEqualTo(originalFunctionalGroup.getFunctionalGroupName());
        assertThat(functionalGroupSheet.getFunctionalGroupDescription()).isEqualTo(originalFunctionalGroup.getFunctionalGroupDescription());
    }

    @Test
    @DisplayName("Should map PartyData correctly")
    void shouldMapPartyDataCorrectly() {
        // Given
        LayerData layerData = getLayerDataFromJson("test-data/layer-data-sample.json");
        
        // When
        OntologyFileData result = LayerDataMapper.INSTANCE.mapToOntologyFileData(layerData);
        
        // Then
        PartySheetData partySheet = result.getParties().get(0);
        PartyData originalParty = layerData.getParties().get(0);
        
        assertThat(partySheet.getPartyCode()).isEqualTo(originalParty.getPartyCode());
        assertThat(partySheet.getPartyName()).isEqualTo(originalParty.getPartyName());
        assertThat(partySheet.getPartyDescription()).isEqualTo(originalParty.getPartyDescription());
        assertThat(partySheet.getAllowMultipleArtifactSources()).isEqualTo(originalParty.getAllowMultipleArtifactSources());
        assertThat(partySheet.getArtifactPrecedence()).isEqualTo(originalParty.getArtifactPrecedence());
    }

    @Test
    @DisplayName("Should map RelationshipData correctly")
    void shouldMapRelationshipDataCorrectly() {
        // Given
        LayerData layerData = getLayerDataFromJson("test-data/layer-data-sample.json");
        
        // When
        OntologyFileData result = LayerDataMapper.INSTANCE.mapToOntologyFileData(layerData);
        
        // Then
        RelationshipSheetData relationshipSheet = result.getRelationships().get(0);
        RelationshipData originalRelationship = layerData.getRelationships().get(0);
        
        assertThat(relationshipSheet.getRelationshipCode()).isEqualTo(originalRelationship.getRelationshipCode());
        assertThat(relationshipSheet.getRelationshipName()).isEqualTo(originalRelationship.getRelationshipName());
        assertThat(relationshipSheet.getRelationshipDescription()).isEqualTo(originalRelationship.getRelationshipDescription());
        assertThat(relationshipSheet.getParentPartyType()).isEqualTo(originalRelationship.getParentPartyType());
        assertThat(relationshipSheet.getChildPartyType()).isEqualTo(originalRelationship.getChildPartyType());
        assertThat(relationshipSheet.getAllowMultipleArtifactSources()).isEqualTo(originalRelationship.getAllowMultipleArtifactSources());
        assertThat(relationshipSheet.getArtifactPrecedence()).isEqualTo(originalRelationship.getArtifactPrecedence());
    }

    @Test
    @DisplayName("Should map BusinessKeyData correctly")
    void shouldMapBusinessKeyDataCorrectly() {
        // Given
        LayerData layerData = getLayerDataFromJson("test-data/layer-data-sample.json");
        
        // When
        OntologyFileData result = LayerDataMapper.INSTANCE.mapToOntologyFileData(layerData);
        
        // Then
        BusinessKeySheetData businessKeySheet = result.getBusinessKeys().get(0);
        BusinessKeyData originalBusinessKey = layerData.getBusinessKeys().get(0);
        
        assertThat(businessKeySheet.getPartyCode()).isEqualTo(originalBusinessKey.getPartyCode());
        assertThat(businessKeySheet.getAttributeCodes()).isEqualTo(originalBusinessKey.getAttributeCodes());
        assertThat(businessKeySheet.getOrder()).isEqualTo(originalBusinessKey.getOrder());
        assertThat(businessKeySheet.getOtherAttributesToValidate()).isEqualTo(originalBusinessKey.getOtherAttributesToValidate());
        assertThat(businessKeySheet.getKeyName()).isEqualTo(originalBusinessKey.getKeyName());
        assertThat(businessKeySheet.getKeyDescription()).isEqualTo(originalBusinessKey.getKeyDescription());
    }

    @Test
    @DisplayName("Should map IDVRuleData correctly")
    void shouldMapIDVRuleDataCorrectly() {
        // Given
        LayerData layerData = getLayerDataFromJson("test-data/layer-data-sample.json");
        
        // When
        OntologyFileData result = LayerDataMapper.INSTANCE.mapToOntologyFileData(layerData);
        
        // Then
        IDVRuleSheetData idRuleSheet = result.getIdRules().get(0);
        IDVRuleData originalIdRule = layerData.getIdRules().get(0);
        
        assertThat(idRuleSheet.getEntityCode()).isEqualTo(originalIdRule.getEntityCode());
        assertThat(idRuleSheet.getAttributeCode()).isEqualTo(originalIdRule.getAttributeCode());
        assertThat(idRuleSheet.getConditionExpression()).isEqualTo(originalIdRule.getConditionExpression());
        assertThat(idRuleSheet.getRuleCode()).isEqualTo(originalIdRule.getRuleCode());
        assertThat(idRuleSheet.getRuleName()).isEqualTo(originalIdRule.getRuleName());
        assertThat(idRuleSheet.getRuleDescription()).isEqualTo(originalIdRule.getRuleDescription());
        assertThat(idRuleSheet.getIsRequired()).isEqualTo(originalIdRule.getIsRequired());
        assertThat(idRuleSheet.getReasonCode()).isEqualTo(originalIdRule.getReasonCode());
        assertThat(idRuleSheet.getReasonDescription()).isEqualTo(originalIdRule.getReasonDescription());
        assertThat(idRuleSheet.getArtifactPrecedence()).isEqualTo(originalIdRule.getArtifactPrecedence());
    }

    @Test
    @DisplayName("Should map CustomRuleData correctly")
    void shouldMapCustomRuleDataCorrectly() {
        // Given
        LayerData layerData = getLayerDataFromJson("test-data/layer-data-sample.json");
        
        // When
        OntologyFileData result = LayerDataMapper.INSTANCE.mapToOntologyFileData(layerData);
        
        // Then
        CustomRuleSheetData customRuleSheet = result.getCustomRules().get(0);
        CustomRuleData originalCustomRule = layerData.getCustomRules().get(0);
        
        assertThat(customRuleSheet.getEntityCode()).isEqualTo(originalCustomRule.getEntityCode());
        assertThat(customRuleSheet.getAttributeCodePattern()).isEqualTo(originalCustomRule.getAttributeCodePattern());
        assertThat(customRuleSheet.getRuleCode()).isEqualTo(originalCustomRule.getRuleCode());
        assertThat(customRuleSheet.getRuleName()).isEqualTo(originalCustomRule.getRuleName());
        assertThat(customRuleSheet.getRuleDescription()).isEqualTo(originalCustomRule.getRuleDescription());
        assertThat(customRuleSheet.getApplyWhen()).isEqualTo(originalCustomRule.getApplyWhen());
        assertThat(customRuleSheet.getRuleExpression()).isEqualTo(originalCustomRule.getRuleExpression());
        assertThat(customRuleSheet.getReasonCode()).isEqualTo(originalCustomRule.getReasonCode());
        assertThat(customRuleSheet.getReasonDescription()).isEqualTo(originalCustomRule.getReasonDescription());
    }

    @Test
    @DisplayName("Should map ArtifactConfigurationData correctly")
    void shouldMapArtifactConfigurationDataCorrectly() {
        // Given
        LayerData layerData = getLayerDataFromJson("test-data/layer-data-sample.json");
        
        // When
        OntologyFileData result = LayerDataMapper.INSTANCE.mapToOntologyFileData(layerData);
        
        // Then
        ArtifactConfigurationSheetData configSheet = result.getArtifactConfigurations().get(0);
        ArtifactConfigurationData originalConfig = layerData.getArtifactConfigurations().get(0);
        
        assertThat(configSheet.getArtifactSchemaCode()).isEqualTo(originalConfig.getArtifactSchemaCode());
        assertThat(configSheet.getArtifactSchemaVersion()).isEqualTo(originalConfig.getArtifactSchemaVersion());
        assertThat(configSheet.getFunctionalGroupCodes()).isEqualTo(originalConfig.getFunctionalGroupCodes());
        assertThat(configSheet.getIsRecyclable()).isEqualTo(originalConfig.getIsRecyclable());
        assertThat(configSheet.getConsiderForMismatchValues()).isEqualTo(originalConfig.getConsiderForMismatchValues());
        assertThat(configSheet.getConsiderForMissingValues()).isEqualTo(originalConfig.getConsiderForMissingValues());
        assertThat(configSheet.getExpirationDateExpression()).isEqualTo(originalConfig.getExpirationDateExpression());
    }

    @Test
    @DisplayName("Should map ArtifactAttributeMappingData correctly")
    void shouldMapArtifactAttributeMappingDataCorrectly() {
        // Given
        LayerData layerData = getLayerDataFromJson("test-data/layer-data-sample.json");
        
        // When
        OntologyFileData result = LayerDataMapper.INSTANCE.mapToOntologyFileData(layerData);
        
        // Then
        ArtifactAttributeMappingSheetData mappingSheet = result.getArtifactAttributeMappings().get(0);
        ArtifactAttributeMappingData originalMapping = layerData.getArtifactAttributeMappings().get(0);
        
        assertThat(mappingSheet.getArtifactSchemaCode()).isEqualTo(originalMapping.getArtifactSchemaCode());
        assertThat(mappingSheet.getSectionCode()).isEqualTo(originalMapping.getSectionCode());
        assertThat(mappingSheet.getAttributeCodeExpression()).isEqualTo(originalMapping.getAttributeCodeExpression());
        assertThat(mappingSheet.getEntityIdentifier()).isEqualTo(originalMapping.getEntityIdentifier());
        assertThat(mappingSheet.getMappingName()).isEqualTo(originalMapping.getMappingName());
        assertThat(mappingSheet.getMappingDescription()).isEqualTo(originalMapping.getMappingDescription());
        assertThat(mappingSheet.getEntityCodePattern()).isEqualTo(originalMapping.getEntityCodePattern());
        assertThat(mappingSheet.getDestinationAttributeCode()).isEqualTo(originalMapping.getDestinationAttributeCode());
        assertThat(mappingSheet.getMapWhen()).isEqualTo(originalMapping.getMapWhen());
    }

    @Test
    @DisplayName("Should map OntologyFileData back to LayerData correctly (bidirectional mapping)")
    void shouldMapOntologyFileDataBackToLayerDataCorrectly() {
        // Given
        LayerData originalLayerData = getLayerDataFromJson("test-data/layer-data-sample.json");

        // When
        OntologyFileData ontologyFileData = LayerDataMapper.INSTANCE.mapToOntologyFileData(originalLayerData);
        LayerData resultLayerData = LayerDataMapper.INSTANCE.mapToLayerData(ontologyFileData);

        // Then
        assertThat(resultLayerData).isNotNull();

        // Verify main collections sizes
        assertThat(resultLayerData.getFunctionalGroups()).hasSize(originalLayerData.getFunctionalGroups().size());
        assertThat(resultLayerData.getParties()).hasSize(originalLayerData.getParties().size());
        assertThat(resultLayerData.getRelationships()).hasSize(originalLayerData.getRelationships().size());
        assertThat(resultLayerData.getBusinessKeys()).hasSize(originalLayerData.getBusinessKeys().size());
        assertThat(resultLayerData.getArtifactSectionMappings()).hasSize(originalLayerData.getArtifactSectionMappings().size());
        assertThat(resultLayerData.getArtifactAttributeMappings()).hasSize(originalLayerData.getArtifactAttributeMappings().size());

        // Verify specific data integrity for ArtifactSectionMapping
        if (!originalLayerData.getArtifactSectionMappings().isEmpty() && !resultLayerData.getArtifactSectionMappings().isEmpty()) {
            ArtifactSectionMappingData originalMapping = originalLayerData.getArtifactSectionMappings().get(0);
            ArtifactSectionMappingData resultMapping = resultLayerData.getArtifactSectionMappings().get(0);

            assertThat(resultMapping.getArtifactSchemaCode()).isEqualTo(originalMapping.getArtifactSchemaCode());
            assertThat(resultMapping.getSectionCodePattern()).isEqualTo(originalMapping.getSectionCodePattern());
            assertThat(resultMapping.getEntityIdentifier()).isEqualTo(originalMapping.getEntityIdentifier());
            assertThat(resultMapping.getMappingName()).isEqualTo(originalMapping.getMappingName());
            assertThat(resultMapping.getEntityType()).isEqualTo(originalMapping.getEntityType());
        }
    }

    @Test
    @DisplayName("Should map individual ArtifactSectionMappingData bidirectionally")
    void shouldMapArtifactSectionMappingDataBidirectionally() {
        // Given
        LayerData layerData = getLayerDataFromJson("test-data/layer-data-sample.json");
        ArtifactSectionMappingData originalData = layerData.getArtifactSectionMappings().get(0);

        // When
        ArtifactSectionMappingSheetData sheetData = ArtifactSectionMappingDataMapper.INSTANCE.toArtifactSectionMappingSheetData(originalData);
        ArtifactSectionMappingData resultData = ArtifactSectionMappingDataMapper.INSTANCE.toArtifactSectionMappingData(sheetData);

        // Then
        assertThat(resultData).isNotNull();
        assertThat(resultData.getArtifactSchemaCode()).isEqualTo(originalData.getArtifactSchemaCode());
        assertThat(resultData.getSectionCodePattern()).isEqualTo(originalData.getSectionCodePattern());
        assertThat(resultData.getEntityIdentifier()).isEqualTo(originalData.getEntityIdentifier());
        assertThat(resultData.getMappingName()).isEqualTo(originalData.getMappingName());
        assertThat(resultData.getMappingDescription()).isEqualTo(originalData.getMappingDescription());
        assertThat(resultData.getEntityType()).isEqualTo(originalData.getEntityType());
        assertThat(resultData.getEntityCodeExpression()).isEqualTo(originalData.getEntityCodeExpression());
        assertThat(resultData.getParentIdentifier()).isEqualTo(originalData.getParentIdentifier());
        assertThat(resultData.getChildIdentifier()).isEqualTo(originalData.getChildIdentifier());
        assertThat(resultData.getMapWhen()).isEqualTo(originalData.getMapWhen());
        assertThat(resultData.getIsIndexableForRecycling()).isEqualTo(originalData.getIsIndexableForRecycling());
    }

    @Test
    @DisplayName("Should map individual ArtifactAttributeMappingData bidirectionally")
    void shouldMapArtifactAttributeMappingDataBidirectionally() {
        // Given
        LayerData layerData = getLayerDataFromJson("test-data/layer-data-sample.json");
        ArtifactAttributeMappingData originalData = layerData.getArtifactAttributeMappings().get(0);

        // When
        ArtifactAttributeMappingSheetData sheetData = ArtifactAttributeMappingDataMapper.INSTANCE.toArtifactAttributeMappingSheetData(originalData);
        ArtifactAttributeMappingData resultData = ArtifactAttributeMappingDataMapper.INSTANCE.toArtifactAttributeMappingData(sheetData);

        // Then
        assertThat(resultData).isNotNull();
        assertThat(resultData.getArtifactSchemaCode()).isEqualTo(originalData.getArtifactSchemaCode());
        assertThat(resultData.getSectionCode()).isEqualTo(originalData.getSectionCode());
        assertThat(resultData.getAttributeCodeExpression()).isEqualTo(originalData.getAttributeCodeExpression());
        assertThat(resultData.getEntityIdentifier()).isEqualTo(originalData.getEntityIdentifier());
        assertThat(resultData.getMappingName()).isEqualTo(originalData.getMappingName());
        assertThat(resultData.getMappingDescription()).isEqualTo(originalData.getMappingDescription());
        assertThat(resultData.getEntityType()).isEqualTo(originalData.getEntityType());
        assertThat(resultData.getEntityCodePattern()).isEqualTo(originalData.getEntityCodePattern());
        assertThat(resultData.getDestinationAttributeCode()).isEqualTo(originalData.getDestinationAttributeCode());
        assertThat(resultData.getMapWhen()).isEqualTo(originalData.getMapWhen());
    }

    @SneakyThrows
    private LayerData getLayerDataFromJson(String fileName) {
        return MapperUtils.getObjectMapper().readValue(getFileAsString(fileName), LayerData.class);
    }

    @SneakyThrows
    private String getFileAsString(String fileName) {
        return FileUtils.readFileToString(getFileFromResources(fileName), StandardCharsets.UTF_8);
    }

    private File getFileFromResources(String fileName) {
        return new File(BASE_PATH.concat(fileName));
    }
}
