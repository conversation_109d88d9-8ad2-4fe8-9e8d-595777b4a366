package pt.kpmg.sph.policy.excel.mappers.ontologies;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import pt.kpmg.sph.entities.model.policy.ontology.Ontology;
import pt.kpmg.sph.entities.model.policy.ontology.OntologyVersion;
import pt.kpmg.sph.policy.excel.data.ontologies.OntologySheetData;

@Mapper
public interface OntologyFileMapper {
  OntologyFileMapper INSTANCE = Mappers.getMapper(OntologyFileMapper.class);

  @Mapping(source = "ontology.code", target = "ontologyCode")
  @Mapping(source = "ontology.name", target = "ontologyName")
  @Mapping(source = "ontologyVersion.version", target = "ontologyVersion")
  @Mapping(source = "ontologyVersion.lastUpdatedOn", target = "lastUpdatedOn")
  @Mapping(source = "ontologyVersion.hashCode", target = "hash")
  OntologySheetData mapToOntologySheetData(Ontology ontology, OntologyVersion ontologyVersion);

  @Mapping(source = "ontologyCode", target = "code")
  @Mapping(source = "ontologyName", target = "name")
  Ontology mapToOntology(OntologySheetData sheetData);

  @Mapping(source = "ontologyVersion", target = "version")
  @Mapping(source = "lastUpdatedOn", target = "lastUpdatedOn")
  @Mapping(source = "hash", target = "hashCode")
  OntologyVersion mapToOntologyVersion(OntologySheetData sheetData);
}
