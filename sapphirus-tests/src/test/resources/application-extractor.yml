sapphirus.extraction.custom-model-extractor:
  extraction-mode:
    default-mode:
      mode: ONE_PAGE_AT_A_TIME
    document-type-modes:
      RCS_FR:
        mode: ALL_PAGES
      RCS_GER:
        mode: ALL_PAGES
  composed-models:
    firstPagesForMultiPageModels: firstPagesForMultiPageModels_v10.18
    otherPagesForMultiPageModels: otherPagesForMultiPageModels_v8.15
    firstPagesForSinglePageModels: firstPagesForSinglePageModels_v8.17
  artifactSchemaCode-composed-models:
    STATEMENT_OF_INCORPORATION_ES:
      multiPages: CPE_ES_v1.0
      singlePage: CPE_ES_v1.0
    STATEMENT_OF_INCORPORATION_NL:
      multiPages: CPE_NL_v1.0
      singlePage: CPE_NL_v1.0
    STATEMENT_OF_INCORPORATION_BE:
      multiPages: CPE_BE_v1.0
      singlePage: CPE_BE_v1.0
    IDCARD_PT:
      multiPages: IDCARD_PT_v1
      singlePage: IDCARD_PT_COMPLETE_v3_4
    IDCARD:
      multiPages: prebuilt-idDocument
      singlePage: prebuilt-idDocument
    IDCARD_ESP:
      multiPages: IDCARD_ESP_v1.1
      singlePage: IDCARD_ESP_COMPLETE_v2.1
    IDCARD_LUX:
      multiPages: IDCARD_LUX_v2.1
      singlePage: IDCARD_LUX_COMPLETE_v1.3
    PASSPORT_PT:
      multiPages: PASSPORT_PT_v3_6
      singlePage: PASSPORT_PT_v3_6
    PASSPORT_NON_PT:
      multiPages: PASSPORTS_NON_PT_v2
      singlePage: PASSPORTS_NON_PT_v2
    PASSPORT:
      multiPages: prebuilt-idDocument
      singlePage: prebuilt-idDocument
    KPMG_CLIENT_ONBOARDING_FORM:
      multiPages: KPMG_FORM_v1
      singlePage: KPMG_FORM_v1
    UBO_LOOKUP:
      multiPages: RCBE_CONSULT_v3.2
      singlePage: RCBE_CONSULT_v3.2
    STATEMENT_OF_INCORPORATION_PT:
      multiPages: STATEMENT_INCORPORATION_PT_multiPages_v1
      singlePage: STATEMENT_INCORPORATION_PT_singlePage_v1
    UBODECLARATION_PT:
      multiPages: RCBE_composed_v3.0
      singlePage: RCBE_composed_v3.0
    UBODECLARATION_DE:
      multiPages: RCBE_DE_v1.0
      singlePage: RCBE_DE_v1.0
    UBODECLARATION_NL:
      multiPages: RCBE_NL_v1.1
      singlePage: RCBE_NL_v1.1
    UBODECLARATION_IE:
      multiPages: RCBE_IE_v1.0
      singlePage: RCBE_IE_v1.0
    UBODECLARATION_IT:
      multiPages: RCBE_IT1_v1.0
      singlePage: RCBE_IT1_v1.0
    UBODECLARATION_BE:
      multiPages: RCBE_BE_v1.0
      singlePage: RCBE_BE_v1.0
    RBE:
      multiPages: RBE_v1
      singlePage: RBE_v1
    RBE_LUX:
      multiPages: RBE_LUX_v1.0
      singlePage: RBE_LUX_v1.0
    RBE_LUX2:
      multiPages: RBE_LUX2_v1.0
      singlePage: RBE_LUX2_v1.0
    RCS:
      multiPages: RCS_v3
      singlePage: RCS_v3
    RCS_LUX:
      multiPages: RCS_LUX_v1.0
      singlePage: RCS_LUX_v1.0
    RCS_LUX2:
      multiPages: RCS_LUX2_v2.1
      singlePage: RCS_LUX2_v2.1
    RCS_LUX3:
      multiPages: RCS_LUX3_v2.0
      singlePage: RCS_LUX3_v2.0
    RCS_FR:
      multiPages: RCS_FR_v0.13
      singlePage: RCS_FR_v0.13
    RCS_GER:
      multiPages: RCS_GER_v0.2
      singlePage: RCS_GER_v0.2
  model-groups:
    certidao-new-other-pages:
      modelId: STATEMENT_INCORPORATION_PT_NEW_OTHER_PAGES_composed_v1
      members:
        - STATEMENT_INCORPORATION_PT_NEW_OTHER_PAGE
        - STATEMENT_INCORPORATION_PT_NEW_OTHER_PAGE_AVERB
        - STATEMENT_INCORPORATION_PT_NEW_EXPIRY
    certidao-old-other-pages:
      modelId: STATEMENT_INCORPORATION_PT_OLD_composed_v1
      members:
        - STATEMENT_INCORPORATION_PT_OLD
        - STATEMENT_INCORPORATION_PT_OLD_EXPIRY
    RCBE:
      modelId: RCBE_composed_v3.0
      members:
        - RCBE_NORMAL
        - RCBE_EMAIL
        - RCBE_FORM
        - RCBE_EXPORT
    RCBE_NORMAL:
      modelId: RCBE_v3.2
      members:
        - RCBE
    RCBE_EMAIL:
      modelId: RCBE_EMAIL_v1n
      members:
        - RCBE_EMAIL
    RCBE_FORM:
      modelId: RCBE_FORM_v2.0n
      members:
        - RCBE_FORM
    RCBE_EXPORT:
      modelId: RCBE_EXPORT_v1n
      members:
        - RCBE_EXPORT
    RCBE_CONSULT:
      modelId: RCBE_CONSULT_v3.2
      members:
        - RCBE_CONSULT
    UBODECLARATION_DE:
      modelId: RCBE_DE_v1.0
      members:
        - UBODECLARATION_DE
    UBODECLARATION_NL:
      modelId: RCBE_NL_v1.1
      members:
        - UBODECLARATION_NL
    UBODECLARATION_IT:
      modelId: RCBE_IT1_v1.0
      members:
        - UBODECLARATION_IT
    UBODECLARATION_IE:
      modelId: RCBE_IE_v1.0
      members:
        - UBODECLARATION_IE
    UBODECLARATION_BE:
      modelId: RCBE_BE_v1.0
      members:
        - UBODECLARATION_BE
    IDCARD:
      modelId: IDCARD_PT_v1
      members:
        - IDCARD_PT_COMPLETE
        - IDCARD_PT_BACK
        - IDCARD_PT_FRONT
    IDCARD_ESP:
      modelId: IDCARD_ESP_v1.1
      members:
        -IDCARD_ESP_COMPLETE
        -IDCARD_ESP_BACK
        -IDCARD_ESP_FRONT
    IDCARD_LUX:
      modelId: IDCARD_LUX_v2.1
      members:
        -IDCARD_LUX_COMPLETE
        -IDCARD_LUX_BACK
        -IDCARD_LUX_FRONT
    KPMGFORM:
      modelId: KPMG_FORM_v1
      members:
        - KPMG_FORM_CLIENT_DATA
        - KPMG_FORM_FOUR_TABLES
        - KPMG_FORM_COLLECTIVE_PERSON
        - KPMG_FORM_INDIVIDUAL_PERSON
        - KPMG_FORM_BENEFICIARY
        - KPMG_FORM_BOARD_MEMBER
        - KPMG_FORM_OTHER_REPRESENTATIVES
    PASSPORT_PT:
      modelId: PASSPORT_PT_v3_6
      members:
        - PASSPORT_PT
    PASSPORT:
      modelId: prebuilt-idDocument
      members:
        - PASSPORT
    PASSPORT_FRA:
      modelId: PASSPORT_FRA_v2_5
      members:
        - PASSPORT_FRA
    PASSPORT_CH:
      modelId: PASSPORT_CH_v1
      members:
        - PASSPORT_CH
    PASSPORT_UK:
      modelId: PASSPORT_UK_v2_2
      members:
        - PASSPORT_UK
    PASSPORT_ITA:
      modelId: PASSPORT_ITA_v1
      members:
        - PASSPORT_ITA
    PASSPORT_SWE:
      modelId: PASSPORT_SWE_v1n
      members:
        - PASSPORT_SWE
    PASSPORT_ESP:
      modelId: PASSPORT_ESP_v1
      members:
        - PASSPORT_ESP
    STATEMENT_INCORPORATION_ES:
      modelId: CPE_ES_v1.0
      members:
        - STATEMENT_INCORPORATION_ES
    STATEMENT_INCORPORATION_NL:
      modelId: CPE_NL_v1.0
      members:
        - STATEMENT_INCORPORATION_NL
    RBE:
      modelId: RBE_v1
      members:
        - RBE
    RBE_LUX:
      modelId: RBE_LUX_v1.0
      members:
        - RBE_LUX
    RBE_LUX2:
      modelId: RBE_LUX2_v1.0
      members:
        - RBE_LUX2
    RCS:
      modelId: RCS_v3
      members:
        - RCS
    RCS_LUX:
      modelId: RCS_LUX_v1.0
      members:
        - RCS_LUX
    RCS_LUX2:
      modelId: RCS_LUX2_v2.1
      members:
        - RCS_LUX2
    RCS_LUX3:
      modelId: RCS_LUX3_v2.0
      members:
        - RCS_LUX3
    RCS_FR:
      modelId: RCS_FR_v0.13
      members:
        - RCS_FR
    RCS_GER:
      modelId: RCS_GER_v0.2
      members:
        - RCS_GER
    STATEMENT_INCORPORATION_BE:
      modelId: CPE_BE_v1.0
      members:
        - STATEMENT_INCORPORATION_BE
  models:
    STATEMENT_INCORPORATION_PT_NEW_FIRST_PAGE: STATEMENT_INCORPORATION_PT_NEW_FIRST_PAGE_v1n
    STATEMENT_INCORPORATION_PT_NEW_OTHER_PAGE: STATEMENT_INCORPORATION_PT_NEW_OTHER_PAGE_v1
    STATEMENT_INCORPORATION_PT_NEW_OTHER_PAGE_AVERB: STATEMENT_INCORPORATION_PT_NEW_OTHER_PAGE_AVERB_v1n
    STATEMENT_INCORPORATION_PT_NEW_EXPIRY: STATEMENT_INCORPORATION_PT_NEW_EXPIRY_v1
    STATEMENT_INCORPORATION_PT_OLD: STATEMENT_INCORPORATION_PT_OLD_v1
    STATEMENT_INCORPORATION_PT_OLD_EXPIRY: STATEMENT_INCORPORATION_PT_OLD_EXPIRY_v1
    STATEMENT_INCORPORATION_ES: CPE_ES_v1.0n
    STATEMENT_INCORPORATION_NL: CPE_NL_v1.0n
    STATEMENT_INCORPORATION_BE: CPE_BE_v1.0n
    RBE: RBE_v1
    RBE_LUX: RBE_LUX_v1.0n
    RBE_LUX2: RBE_LUX2_v1.0n
    RCS: RCS_v3
    RCS_LUX: RCS_LUX_v1.0
    RCS_LUX2: RCS_LUX2_v2.1n
    RCS_LUX3: RCS_LUX3_v2.0n
    RCS_FR: RCS_FR_v0.13
    RCS_GER: RCS_GER_v0.2
    RCBE: RCBE_v3.2
    UBODECLARATION_DE: RCBE_DE_v1.0n
    UBODECLARATION_IE: RCBE_IE_v1.0n
    UBODECLARATION_NL: RCBE_NL_v1.1n
    UBODECLARATION_IT: RCBE_IT1_v1.0n
    UBODECLARATION_BE: RCBE_BE_v1.0n
    RCBE_EMAIL: RCBE_EMAIL_v1n
    RCBE_FORM: RCBE_FORM_v2.0n
    RCBE_EXPORT: RCBE_EXPORT_v1n
    RCBE_CONSULT: RCBE_CONSULT_v3.2
    IDCARD_PT_FRONT: IDCARD_PT_FRONT_v1
    IDCARD_PT_BACK: IDCARD_PT_BACK_v1
    IDCARD_PT_COMPLETE: IDCARD_PT_COMPLETE_v3_4
    IDCARD_ESP_FRONT: IDCARD_ESP_FRONT_v2.0
    IDCARD_ESP_BACK: IDCARD_ESP_BACK_v2.1
    IDCARD_ESP_COMPLETE: IDCARD_ESP_COMPLETE_v2.1
    IDCARD_ESP_FRONT_NEW: IDCARD_ESP_FRONT_NEW_v1.0
    IDCARD_ESP_FRONT_OLD: IDCARD_ESP_FRONT_OLD_v1.1
    IDCARD_ESP_BACK_NEW: IDCARD_ESP_BACK_NEW_v1.3
    IDCARD_ESP_BACK_OLD: IDCARD_ESP_BACK_OLD_v1.2
    IDCARD_ESP_COMPLETE_NEW: IDCARD_ESP_COMPLETE_NEW_v1.3
    IDCARD_ESP_COMPLETE_OLD: IDCARD_ESP_COMPLETE_OLD_v1.2
    IDCARD_LUX_FRONT: IDCARD_LUX_FRONT_v2.4
    IDCARD_LUX_BACK: IDCARD_LUX_BACK_v2.0
    IDCARD_LUX_COMPLETE: IDCARD_LUX_COMPLETE_v1.3
    KPMG_FORM_CLIENT_DATA: KPMG_FORM_CLIENT_DATA_v1
    KPMG_FORM_FOUR_TABLES: KPMG_FORM_FOUR_TABLES_v1
    KPMG_FORM_COLLECTIVE_PERSON: KPMG_FORM_COLLECTIVE_PERSON_v1
    KPMG_FORM_INDIVIDUAL_PERSON: KPMG_FORM_INDIVIDUAL_PERSON_v1
    KPMG_FORM_BENEFICIARY: KPMG_FORM_BENEFICIARY_v1
    KPMG_FORM_BOARD_MEMBER: KPMG_FORM_BOARD_MEMBER_v1
    KPMG_FORM_OTHER_REPRESENTATIVES: KPMG_FORM_OTHER_REPRESENTATIVES_v1
    PASSPORT_PT: PASSPORT_PT_v3_6
    PASSPORT_FRA: PASSPORT_FRA_v2_5
    PASSPORT_CH: PASSPORT_CH_v1
    PASSPORT_UK: PASSPORT_UK_v2_2
    PASSPORT_ITA: PASSPORT_ITA_v1
    PASSPORT_SWE: PASSPORT_SWE_v1n
    PASSPORT_ESP: PASSPORT_ESP_v1
  postProcessing:
    KPMG_FORM_INDIVIDUAL_PERSON: pt.kpmg.sph.extractionextractor.service.processor.KpmgFormSelectionMarksPostProcessor
    KPMG_FORM_BENEFICIARY: pt.kpmg.sph.extractionextractor.service.processor.KpmgFormSelectionMarksPostProcessor
    KPMG_FORM_BOARD_MEMBER: pt.kpmg.sph.extractionextractor.service.processor.KpmgFormSelectionMarksPostProcessor
    KPMG_FORM_OTHER_REPRESENTATIVES: pt.kpmg.sph.extractionextractor.service.processor.KpmgFormSelectionMarksPostProcessor
    RCBE_CONSULT: pt.kpmg.sph.extraction.extractor.processor.RcbeLookupTaxIDNumberProcessor
    RBE_LUX: pt.kpmg.sph.extractionextractor.service.processor.RbeLuxSwapNameProcessor
    RBE_LUX2: pt.kpmg.sph.extractionextractor.service.processor.RbeLuxSwapNameProcessor
    CPE_BE: pt.kpmg.sph.extractionextractor.service.processor.CpeBeSwapNameProcessor
  rbeLuxExcludedNames:
    - "Name, first name(s)"
    - "Nom, Prénom(s)"
    - "Name, Vorname(en)"
  bigBoundingBox:
    - KPMGFORM
  azure:
    endpoint: https://ptazreuw-forms-dev-23077.cognitiveservices.azure.com
    key: key #507d572c168b4d1fa333c1f1e3c895a6 #${AZURE_KEY_CLOUD}
  columnsPattern: "^(COLUMN|column)[0-9]+$"