spring:
  servlet:
    multipart:
      enabled: true
  cloud:
    function:
      definition: >
        ${base-test.function.definition};
        artifactApiServiceIngestionArtifactConsumer-Silver;
        artifactApiServiceIngestionArtifactConsumer-Gold;
        artifactApiServiceDataArtifactConsumer-Silver;
        artifactApiServiceDataArtifactConsumer-Gold;
        artifactApiServiceDataArtifactSupplier-Silver;
        artifactApiServiceDataArtifactSupplier-Gold;
        artifactApiServiceCaseFileConsumer-Silver;
        artifactApiServiceCaseFileConsumer-Gold;     
        artifactQualityServiceDataArtifactConsumer-Silver;
        artifactQualityServiceDataArtifactConsumer-Gold;
        artifactQualityServiceDataArtifactSupplier-Silver;
        artifactQualityServiceDataArtifactSupplier-Gold;
        fragmentStylistServiceDataArtifactConsumer-Silver;
        fragmentStylistServiceDataArtifactConsumer-Gold;
        fragmentStylistServiceCaseArtifactSupplier-Silver;
        fragmentStylistServiceCaseArtifactSupplier-Gold;
        caseSewingServiceCaseFragmentsConsumer-Silver;
        caseSewingServiceCaseFragmentsConsumer-Gold;
        caseSewingServiceBlueprintCaseFilesSupplier-Silver;
        caseSewingServiceBlueprintCaseFilesSupplier-Gold;
        caseSewingServiceDataArtifactConsumer-Silver;
        caseSewingServiceDataArtifactConsumer-Gold;
        caseStylistServiceCaseFileFunction-Silver;
        caseStylistServiceCaseFileFunction-Gold;
        caseReviewServiceCaseFileFunction-Silver;
        caseReviewServiceCaseFileFunction-Gold;
        caseStorageServiceCaseFileFunction-Silver;
        caseStorageServiceCaseFileFunction-Gold;

    stream:
      bindings:
        # artifact-api-service
        artifactApiServiceIngestionArtifactConsumer-Silver-in-0:
          group: sph_artifact_api_service
          destination: ${kafka.topics.sapphirus.ingestion.ingestion-artifacts}
        artifactApiServiceIngestionArtifactConsumer-Gold-in-0:
          group: sph_artifact_api_service
          destination: ${kafka.topics.sapphirus.ingestion.ingestion-artifacts-2}
        artifactApiServiceDataArtifactConsumer-Silver-in-0:
          group: sph_artifact_api_service
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}
        artifactApiServiceDataArtifactConsumer-Gold-in-0:
          group: sph_artifact_api_service
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts-2}
        artifactApiServiceDataArtifactSupplier-Silver-out-0:
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}
        artifactApiServiceDataArtifactSupplier-Gold-out-0:
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts-2}
        artifactApiServiceCaseFileConsumer-Silver-in-0:
          group: sph_artifact_api_service
          destination: ${kafka.topics.sapphirus.review.case-files}
        artifactApiServiceCaseFileConsumer-Gold-in-0:
          group: sph_artifact_api_service
          destination: ${kafka.topics.sapphirus.review.case-files-2}

        # artifact-quality-service
        artifactQualityServiceDataArtifactConsumer-Silver-in-0:
          group: sapphirus-artifact-quality
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}
        artifactQualityServiceDataArtifactConsumer-Gold-in-0:
          group: sapphirus-artifact-quality
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts-2}
        artifactQualityServiceDataArtifactSupplier-Silver-out-0:
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}
        artifactQualityServiceDataArtifactSupplier-Gold-out-0:
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts-2}

        # case-review-service
        caseReviewServiceCaseFileFunction-Silver-in-0:
          group: sph_case_review
          destination: ${kafka.topics.sapphirus.assembly.case-enhancements}
        caseReviewServiceCaseFileFunction-Gold-in-0:
          group: sph_case_review
          destination: ${kafka.topics.sapphirus.assembly.case-enhancements-2}
        caseReviewServiceCaseFileFunction-Silver-out-0:
          destination: ${kafka.topics.sapphirus.review.case-revisions}
        caseReviewServiceCaseFileFunction-Gold-out-0:
          destination: ${kafka.topics.sapphirus.review.case-revisions-2}

        # case-sewing-service
        caseSewingServiceCaseFragmentsConsumer-Silver-in-0:
          group: case-sewing
          destination: ${kafka.topics.sapphirus.ingestion.case-fragments}
        caseSewingServiceCaseFragmentsConsumer-Gold-in-0:
          group: case-sewing
          destination: ${kafka.topics.sapphirus.ingestion.case-fragments-2}
        caseSewingServiceBlueprintCaseFilesSupplier-Silver-out-0:
          destination: ${kafka.topics.sapphirus.assembly.case-sketches}
        caseSewingServiceBlueprintCaseFilesSupplier-Gold-out-0:
          destination: ${kafka.topics.sapphirus.assembly.case-sketches-2}
        caseSewingServiceDataArtifactConsumer-Silver-in-0:
          group: case-sewing
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}
        caseSewingServiceDataArtifactConsumer-Gold-in-0:
          group: case-sewing
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts-2}

        # case-storage-service
        caseStorageServiceCaseFileFunction-Silver-in-0:
          group: case-storage
          destination: ${kafka.topics.sapphirus.review.case-revisions}
        caseStorageServiceCaseFileFunction-Gold-in-0:
          group: case-storage
          destination: ${kafka.topics.sapphirus.review.case-revisions-2}
        caseStorageServiceCaseFileFunction-Silver-out-0:
          destination: ${kafka.topics.sapphirus.review.case-files}
        caseStorageServiceCaseFileFunction-Gold-out-0:
          destination: ${kafka.topics.sapphirus.review.case-files-2}

        # case-stylist-service
        caseStylistServiceCaseFileFunction-Silver-in-0:
          group: sph_case_stylist
          destination: ${kafka.topics.sapphirus.assembly.case-sketches}
        caseStylistServiceCaseFileFunction-Gold-in-0:
          group: sph_case_stylist
          destination: ${kafka.topics.sapphirus.assembly.case-sketches-2}
        caseStylistServiceCaseFileFunction-Silver-out-0:
          destination: ${kafka.topics.sapphirus.assembly.case-enhancements}
        caseStylistServiceCaseFileFunction-Gold-out-0:
          destination: ${kafka.topics.sapphirus.assembly.case-enhancements-2}

        # fragment-stylist-service
        fragmentStylistServiceDataArtifactConsumer-Silver-in-0:
          group: sph_fragment_stylist
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}
        fragmentStylistServiceDataArtifactConsumer-Gold-in-0:
          group: sph_fragment_stylist
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts-2}
        fragmentStylistServiceCaseArtifactSupplier-Silver-out-0:
          destination: ${kafka.topics.sapphirus.ingestion.case-fragments}
        fragmentStylistServiceCaseArtifactSupplier-Gold-out-0:
          destination: ${kafka.topics.sapphirus.ingestion.case-fragments-2}
  data:
    mongodb:
      gridfs:
        database: test
  neo4j:
    uri: http://localhost:8080
    authentication:
      username: username
      password: password

sapphirus.event-lanes:
  enabled: true
  lanes:
    - Silver
    - Gold
  kafka:
    enabled: true
    lane-event-header: X-Lane
    default-supplier-lane: Silver
    consumers:
        # Artifact API
      - artifactApiServiceIngestionArtifactConsumer
      - artifactApiServiceDataArtifactConsumer
      - artifactApiServiceCaseFileConsumer
        # Aritfact Quality
      - artifactQualityServiceDataArtifactConsumer
        # Fragment Stylist
      - fragmentStylistServiceDataArtifactConsumer
        # Case Sewing
      - caseSewingServiceCaseFragmentsConsumer
      - caseSewingServiceDataArtifactConsumer
    suppliers:
        # Artifact API
      - artifactApiServiceDataArtifactSupplier
        # Artifact Quality
      - artifactQualityServiceDataArtifactSupplier
        # Case Sewing
      - caseSewingServiceBlueprintCaseFilesSupplier
        # Fragment Stylist
      - fragmentStylistServiceCaseArtifactSupplier
    functions:
        # Case Review
      - caseReviewServiceCaseFileFunction
        # Case Stylist
      - caseStylistServiceCaseFileFunction
        # Case Storage
      - caseStorageServiceCaseFileFunction
  rest:
    enabled: true
    default-api-lane: Silver
    lane-api-header: X-Api-Lane
    inbound-interceptor:
      enabled: true
    outbound-interceptor:
      enabled: true
    feign-outbound-interceptor:
      enabled: true
  enrichers:
    enabled: true
    lane-name: laneTest
    case-file-enricher:
      order: 0
      enabled: true
    artifact-enricher:
      order: 0
      enabled: true
    lane-header-enricher:
      order: 1
      enabled: true

graphql:
  servlet:
    mapping: /graphql
    enabled: true
    corsEnabled: true

openapi:
  caseChangeAnalyser.base-path: /case-change-analyser
  extractionStorage.base-path: /extraction-storage
  contentManager.base-path: /content-manager
  policyArtifactSchemas.base-path: /policy
  policyFiles.base-path: /policy
  policyEnumGroups.base-path: /policy
  policyOntologies.base-path: /policy
  policyRuntimeOntologies.base-path: /policy
  artifactAdmin.base-path: /artifact-api
  artifactManagement.base-path: /artifact-api
  extractionManager.base-path: /extraction-manager
  caseStorage.base-path: /case-storage
  dispatcher.base-path: /dispatcher
  artifactQuality.base-path: /artifact-quality
  party.base-path: /party-manager
  claimcheck.base-path: /claimcheck-api
  sPHArtifactRecycling.base-path: /artifact-recycling
  extractionVerifier.base-path: /extraction-verifier
  tenantManagement.base-path: /tenant-management

# common
sapphirus.common:
  expression:
    max-length: 30000
    max-cached-entries: 10000

# multitenancy
sapphirus.multitenancy:
  enabled: true

# artifact-api
sapphirus.ingestion:
  artifact-api:
    service-accounts:
      - service-account-kyc
      - service-account-postman
      - service-account-sapphirus
    event-lanes:
      enabled: true
      lane-metadata-name: X-Lane
    artifact-expiration-task:
      cron: "*/2 * * * * *" # execute every two seconds

# artifact-quality
sapphirus.artifact-quality:
  tax-id-number-validations:
    rules:
      INDIVIDUAL:
        ALB.regex: [ "^([0-9A-T])[0-9]{8}[A-W]$" ]
        AND.regex: [ "^[FE]-[0-9]{6}-[A-Z]$|^[FE][0-9]{6}[A-Z]$" ]
        ARM.regex: [ "^[0-9]{10}$|[1-9][0-9]{7}" ]
        ARG.regex: [ "^[0-9]{11}$" ]
        AUS.regex: [ "^[0-9]{8,9}$" ]
        AUT.regex: [ "^[0-9]{2}-[0-9]{3}\\/[0-9]{4}$|^[0-9]{9}$" ]
        ABW.regex: [ "^[0-9]{7,8}$" ]
        AZE.regex: [ "^[0-9]{9}[2]$|^[0-9A-Z]{7}$" ]
        BLZ.regex: [ "^[0-9]{6}(10)?$" ]
        BEL.regex: [ "^[0-9]{11}$" ]
        BRA.regex: [ "^[0-9]{11}$" ]
        BGR.regex: [ "^[0-9]{10}$" ]
        BES.regex: [ "^[356][78][0-9]{5}[1-9][0-9]$" ]
        BLR.regex: [ "^[A-Z]{2}[0-9]{7}$" ]
        CAN.regex: [ "^[0-9]{9}$" ]
        CHL.regex: [ "^[0-9]{1,2}\\.[0-9]{3}\\.[0-9]{3}-[0-9K]$" ]
        CHN.regex: [ "^[0-9]{17}[0-9X]$|^[CWHMT][0-9]{16}[0-9A-Z]$|^J[0-9]{14}$" ]
        COL.regex: [ "^[0-9]{8}$|^[0-9]{10}$" ]
        CRI.regex: [ "^[0-9]-[0-9]{4}-[0-9]{4}$|^[0-9]{11}$" ]
        HRV.regex: [ "^[0-9]{11}$" ]
        CUW.regex: [ "^1[78][0-9]{5}[1-9][0-9]$" ]
        CYP.regex: [ "^[0-9]{8}[A-Z]$" ]
        CZE.regex: [ "^[0-9]{9,10}$" ]
        DNK.regex: [ "^[0-9]{6}-[0-9]{4}$" ]
        EST.regex: [ "^[1-6][0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])[0-9]{4}$" ]
        ECU.regex: [ "^[0-9]{13}$" ]
        FRO.regex: [ "^[0-9]{6}-?[0-9]{3}$" ]
        FIN.regex: [ "^[0-9]{6}[-\\+A][0-9]{3}[0-9A-Z]$" ]
        FRA.regex: [ "^[0-3][0-9]{12}$|^[0-9]{1,5}[A-Z]$" ]
        DEU.regex: [ "^[0-9]{11,12}$" ]
        GIB.regex: [ "^[0-9]{1,6}$" ]
        GRC.regex: [ "^[0-9]{9}$" ]
        GRL.regex: [ "^[0-9]{6}-?[0-9]{4}$" ]
        GGY.regex: [ "^GY[0-9]{6}$|^[A-Z]{2}[0-9]{6}[A-DFMP]$|^(JY[0-9]{6}[A-Z])$|^[0-9][A-Z]{1,2}[0-9]{6}B{0,1}$" ]
        HKG.regex: [ "^[A-Z]{1,2}[0-9]{6}[0-9A]$" ]
        HUN.regex: [ "^[1-8][0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])[0-9]{4}$|^8[0-9]{9}$" ]
        ISL.regex: [ "^[0-9]{6}-?[0-9]{4}$" ]
        IND.regex: [ "^[A-Z]{3}P[A-Z][0-9]{4}[A-Z]$" ]
        IMN.regex: [ "^(H[0-9]{6}(-[0-9]{2})?)$|^[A-Z]{2}[0-9]{6}[A-DFMP]$" ]
        IDN.regex: [ "^[0-9]{2}\\.[0-9]{3}\\.[0-9]{3}\\.[0-9]-[0-9]{3}\\.[0-9]{3}$|^[0-9]{15}$" ]
        IRL.regex: [ "^[0-9]{7}[A-Z]{1,2}$" ]
        ISR.regex: [ "^[0-9]{9}$" ]
        ITA.regex: [ "^[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]$" ]
        JAM.regex: [ "^[0-9]{9}$" ]
        JPN.regex: [ "^[0-9]{12}$" ]
        JEY.regex: [ "^[0-9]{10}$|^[A-Z]{2}[0-9]{6}[A-DFMP]$" ]
        KWT.regex: [ "^[0-9]*$" ]
        KEN.regex: [ "^[0-9]{11}$" ]
        KGZ.regex: [ "^[1-2](0[1-9]|[12][0-9]|3[01])(0[1-9]|1[0-2])(19[0-9]{2}|20[0-9]{2})[0-9]{6}$" ]
        KOR.regex: [ "^[0-9]{6}-[0-9]{7}$" ]
        KAZ.regex: [ "^[0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[1|2][0-9]|3[0|1])[0-9]{6}$" ]
        LBN.regex: [ "^[0-9]+$" ]
        LVA.regex: [ "^[0-9]{11}$" ]
        LIE.regex: [ "^[0-9]{1,12}$" ]
        LTU.regex: [ "^[0-9]{11}$" ]
        LUX.regex: [ "^[0-9]{13}$" ]
        MDA.regex: [ "^2[0-9]{12}$" ]
        MAF.regex: [ "^[0-9]{1,5}[A-Z]$" ]
        MAC.regex: [ "^0[0-9]{7}$" ]
        MYS.regex: [ "^([SO]G)\\s[0-9]{1,11}$" ]
        MLT.regex: [ "^[0-9]{3,7}[MGAPLHBZ]$|^[0-9]{9}$" ]
        MHL.regex: [ "^([0-9]{4}-04)$|^(04-[0-9]{4})$|^[0-9]{7,8}$" ]
        MUS.regex: [ "^[178][0-9]{7}$" ]
        MEX.regex: [ "^[A-Z]{4}[0-9]{6}[A-Z0-9]{3}$" ]
        NRU.regex: [ "^[0-9]{2}-[0-9]{5}-[0-9]-[0-9]$" ]
        NLD.regex: [ "^[0-9]{9}$" ]
        NZL.regex: [ "^[0-9]{8,9}$" ]
        NOR.regex: [ "^[0-9]{11}$" ]
        NGA.regex: [ "^[0-9]{8}$" ]
        PAK.regex: [ "^[0-9]{13}$" ]
        PER.regex: [ "^1[05][0-9]{9}$|^[0-9]{8}$" ]
        PAN.regex: [ "^[0-9]{5}-[0-9]{2}-[0-9]{6}$|^[0-9]{13}$" ]
        PRY.regex: [ "^[A-Z]{4}-[0-9]{6}[A-Z0-9]$" ]
        PHL.regex: [ "^[0-9]{9}$" ]
        POL.regex: [ "^[0-9]{11}$" ]
        PRT.regex: [ "^([1-3][0-9]{8})$|^(45[0-9]{7})$" ]
        ROU.regex: [ "^[1-9][0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[1|2][0-9]|3[0|1])(0[1-9]|[1-3][0-9]|4[0-7]|51|52)[0-9]{4}$|^90{3}[0-9]{8}$" ]
        RUS.regex: [ "^[0-9]{12}$" ]
        LCA.regex: [ "^[0-9]{1,6}$" ]
        SMR.regex: [ "^[0-9]{1,9}$" ]
        SYC.regex: [ "^[0-9]{2}2[0-9]{6}$" ]
        SGP.regex: [ "^[STFG][0-9]{7}[A-Z]$" ]
        SVK.regex: [ "^[0-9]{9,10}$" ]
        SVN.regex: [ "^[1-9][0-9]{7}$" ]
        SXM.regex: [ "^4[78][0-9]{5}[1-9][0-9]$" ]
        ZAF.regex: [ "^[01239][0-9]{3}\\/[0-9]{3}\\/[0-9]{3}$" ]
        ESP.regex: [ "^[0-9LMKXYZ][0-9]{7}[A-Z]$" ]
        SWE.regex: [ "^(19|20)\\d{10}$" ]
        CHE.regex: [ "^756\\.[0-9]{4}\\.[0-9]{4}\\.[0-9]{2}$|^756[0-9]{10}$" ]
        VCT.regex: [ "^[0-9]*$" ]
        SAU.regex: [ "^[0-9]{10}$" ]
        TWN.regex: [ "^[A-Z][0-9]{9}$" ]
        THA.regex: [ "^[0-9]{13}$" ]
        TJK.regex: [ "^[0-9]{2}[5-9][0-9]{6}$" ]
        TTO.regex: [ "^[0-9]{9}-[0-9]$" ]
        TUR.regex: [ "^[0-9]{10,11}$" ]
        GBR.regex: [ "^[0-9]{10}$|^(?!BG)(?!GB)(?!NK)(?!KN)(?!TN)(?!NT)(?!ZZ)(?:[A-CEGHJ-PR-TW-Z][A-CEGHJ-NPR-TW-Z])(?:\\s*\\d\\s*){6}([A-DFMP]|\\s)$" ]
        USA.regex: [ "^[0-9]{9}$" ]
        VEN.regex: [ "^[A-Z]-[0-9]{8}-[0-9]$" ]
        URY.regex: [ "^[0-9]{8,9}$" ]
        UZB.regex: [ "^[4-7][0-9]{8}$" ]
      COMPANY:
        ALB.regex: [ "^([0-9A-T])[0-9]{8}[A-W]$" ]
        AND.regex: [ "^[FEALCDGOPU]-[0-9]{6}-[A-Z]$|^[FEALCDGOPU][0-9]{6}[A-Z]$" ]
        ARM.regex: [ "^[0-9]{10}$|^[0-9]{8}$" ]
        ARG.regex: [ "^[0-9]{11}$" ]
        AUS.regex: [ "^[0-9]{8,9}$|^[0-9]{11}$" ]
        AUT.regex: [ "^[0-9]{2}-[0-9]{3}\\/[0-9]{4}$|^[0-9]{9}$" ]
        ABW.regex: [ "^[0-9]{7,8}$" ]
        AZE.regex: [ "^[0-9]{9}[1-2]$|^[0-9A-Z]{7}$" ]
        BLZ.regex: [ "^[0-9]{6}(10|13|66)?$" ]
        BEL.regex: [ "^[0-9]{10,12}$" ]
        BRA.regex: [ "^[0-9]{11}$|^[0-9]{2}\\.[0-9]{3}\\.[0-9]{3}\\/[0-9]{4}-[0-9]{2}$|^[0-9]{14}$" ]
        BGR.regex: [ "^[0-9]{9,10}$" ]
        BES.regex: [ "^[356][178][0-9]{5}[1-9][0-9]$" ]
        BLR.regex: [ "^[A-Z]{2}[0-9]{7}$|^[0-9]{9}$" ]
        CAN.regex: [ "^[0-9]{8,9}$|^[0-9]{9}[A-Z]{2}[0-9]{4}$" ]
        CHL.regex: [ "^[0-9]{1,2}\\.[0-9]{3}\\.[0-9]{3}-[0-9K]$" ]
        CHN.regex: [ "^[0-9]{8}[0-9A-HJ-NPQRTUWXY]{10}$|^[0-9]{15}$|^[CWHMT][0-9]{16}[0-9A-Z]$|^J[0-9]{14}$" ]
        COL.regex: [ "^[0-9]{8,10}$" ]
        CRI.regex: [ "^[0-9]{10,11}$|^[0-9]-[0-9]{4}-[0-9]{4}$|^[0-9]-[0-9]{3}-[0-9]{6}$" ]
        HRV.regex: [ "^[0-9]{11}$" ]
        CUW.regex: [ "^1[178][0-9]{5}[1-9][0-9]$" ]
        CYP.regex: [ "^[0-9]{8}[A-Z]$" ]
        CZE.regex: [ "^[0-9]{8,10}$" ]
        DNK.regex: [ "^(((CVR)|(SE))\\s)?[0-9]{8}$|^[0-9]{6}-[0-9]{4}$" ]
        EST.regex: [ "^[189][0-9]{7}$|^[1-6][0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])[0-9]{4}$" ]
        ECU.regex: [ "^[0-9]{13}$" ]
        FRO.regex: [ "^[0-9]*$|^[0-9]{6}-?[0-9]{3}$" ]
        FIN.regex: [ "^[0-9]{6}[-\\+A][0-9]{3}[0-9A-Z]$|^[0-9]{7}-[0-9]$" ]
        FRA.regex: [ "^[0-3][0-9]{12}$|^[0-9]{9}$|^[0-9]{1,5}[A-Z]$|^[0-9]{14}$|^[0-9A-Za-z]{1,11}$" ]
        DEU.regex: [ "^[0-9]{10,12}$" ]
        GIB.regex: [ "^[0-9]{1,6}$" ]
        GRC.regex: [ "^[0-9]{9}$" ]
        GRL.regex: [ "^[0-9]{10}$|^[0-9]{8}$" ]
        GGY.regex: [ "^GY[0-9]{6}$|^[A-Z]{2}[0-9]{6}[A-DFMP]$|^(JY[0-9]{6}[A-Z])$|^[0-9]{1,6}$|^((CH|NP)[0-9]{1,3})$|^[0-9][A-Z]{1,2}[0-9]{6}B{0,1}$" ]
        HKG.regex: [ "^[A-Z]{1,2}[0-9]{6}[0-9A]$|^[0-9]{8}$" ]
        HUN.regex: [ "^[1-8][0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])[0-9]{4}$|^8[0-9]{9}$|^[0-9]{8}-[1-5]-[0-9]{2}$|^[0-9]{8}[1-5][0-9]{2}$" ]
        ISL.regex: [ "^[0-9]{6}-?[0-9]{4}$" ]
        IND.regex: [ "^[A-Z]{3}[PFCAHTBLJG][A-Z][0-9]{4}[A-Z]$" ]
        IMN.regex: [ "^[HCX][0-9]{6}(-[0-9]{2})?$|^[HCX][0-9]{6}-[0-9]{2}Q[0-9]{2}?$|^[A-Z]{2}[0-9]{6}[A-DFMP]$" ]
        IDN.regex: [ "^[0-9]{2}\\.[0-9]{3}\\.[0-9]{3}\\.[0-9]-[0-9]{3}\\.[0-9]{3}$|^[0-9]{15}$" ]
        IRL.regex: [ "^([0-9]{7}[A-Z]{1,2})$|^(CHY\\s[0-9]{1,5})$" ]
        ISR.regex: [ "^[0-9]{9}$" ]
        ITA.regex: [ "^[0-9]{11}$|^[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]$" ]
        JAM.regex: [ "^[0-9]{9}$" ]
        JPN.regex: [ "^[0-9]{12,13}$" ]
        JEY.regex: [ "^[0-9]{10}$|^[A-Z]{2}[0-9]{6}[A-DFMP]$|^[C-E][A-Z][0-9]{1,5}$" ]
        KWT.regex: [ "^[0-9]*$" ]
        KEN.regex: [ "^[0-9]{11}$" ]
        KGZ.regex: [ "^[1-2](0[1-9]|[12][0-9]|3[01])(0[1-9]|1[0-2])(19[0-9]{2}|20[0-9]{2})[0-9]{6}$|^0(0[1-9]|[12][0-9]|3[01])(0[1-9]|1[0-2])(19[0-9]{2}|20[0-9]{2})[0-9]{5}$" ]
        KOR.regex: [ "^[0-9]{3}-[0-9]{2}-[0-9]{5}$|^[0-9]{6}-[0-9]{7}$" ]
        KAZ.regex: [ "^[0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[1|2][0-9]|3[0|1])[0-9]{6}$|(0[1-9]|1[0-2])[0-9]{2}4[0-2][0-9]{6}" ]
        LVA.regex: [ "^[0-9]{11}$" ]
        LIE.regex: [ "^[0-9]{1,12}$" ]
        LTU.regex: [ "^[0-9]{9,10}$" ]
        LUX.regex: [ "^[0-9]{11}$|^[0-9]{13}$" ]
        MDA.regex: [ "^[1-2][0-9]{12}$" ]
        MAF.regex: [ "^[0-9]{1,5}[A-Z]$|^[0-9]{9}$" ]
        MAC.regex: [ "^[08][0-9]{7}$" ]
        MYS.regex: [ "^([SO]G)\\s[0-9]{1,11}$|^[A-Z]{1,2}\\s[0-9]{1,10}$" ]
        MLT.regex: [ "^[0-9]{3,7}[MGAPLHBZ]$|^[0-9]{9}$" ]
        MHL.regex: [ "^([0-9]{4}-04)$|^(04-[0-9]{4})$|^[0-9]{7,8}$" ]
        MUS.regex: [ "^[12378][0-9]{7}$" ]
        MEX.regex: [ "^[A-Z]{3,4}[0-9]{6}[A-Z0-9]{3}$" ]
        NRU.regex: [ "^[0-9]{2}-[0-9]{5}-[0-9]-[0-9]$" ]
        NLD.regex: [ "^[0-9]{9}$" ]
        NZL.regex: [ "^[0-9]{8,9}$" ]
        NOR.regex: [ "^[89][0-9]{8}([A-Z]{3})?$|^[0-9]{11}$" ]
        NGA.regex: [ "^[0-9]{8}(-[0-9]{4})?$" ]
        PAK.regex: [ "^[0-9]{13}$" ]
        PER.regex: [ "^(10|15|20)[0-9]{9}$|^[0-9]{8}$" ]
        PAN.regex: [ "^[0-9]{5}-[0-9]{2}-[0-9]{6}$|^[0-9]{13}$" ]
        PRY.regex: [ "^[A-Z]{4}-[0-9]{6}[A-Z0-9]$" ]
        PHL.regex: [ "^[0-9]{9}$|^[0-9]{12}$" ]
        POL.regex: [ "^[0-9]{10,11}$" ]
        PRT.regex: [ "^[1-3][0-9]{8}$|^45[0-9]{7}$|^[5-6][0-9]{8}$|^(70|71|72|74|77|78|90|91|92|98|99)[0-9]{7}$" ]
        ROU.regex: [ "^[1-9][0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[1|2][0-9]|3[0|1])(0[1-9]|[1-3][0-9]|4[0-7]|51|52)[0-9]{4}$|^90{3}[0-9]{8}$|^[1-9]\\d{1,9}$" ]
        RUS.regex: [ "^[0-9]{10}$|^[0-9]{12}$" ]
        LCA.regex: [ "^[0-9]{1,6}$" ]
        SMR.regex: [ "^[0-9]{1,9}$|^SM[0-9]{5}$" ]
        SYC.regex: [ "^[0-9]{2}[123457][0-9]{6}$" ]
        SGP.regex: [ "^((([0-9]{4,5})|(F(000|\\s{3})[0-9])|([ST][0-9]{2}[A-Z]{2})|A[0-9]{3})[0-9]{4}[A-Z])$|^[STFG][0-9]{7}[A-Z]$" ]
        SVK.regex: [ "^[0-9]{9,10}$" ]
        SVN.regex: [ "^[1-9][0-9]{7}$" ]
        SXM.regex: [ "^4[178][0-9]{5}[1-9][0-9]$" ]
        ZAF.regex: [ "^[01239][0-9]{3}\\/[0-9]{3}\\/[0-9]{3}$" ]
        ESP.regex: [ "^[0-9LMKXYZ][0-9]{7}[A-Z]$|^[A-HJPQSNUVW][0-9]{7}[0-9A-J]$" ]
        SWE.regex: [ "^(16)\\d{10}$" ]
        CHE.regex: [ "^CHE-[0-9]{3}\\.[0-9]{3}\\.[0-9]{3}$|^756\\.[0-9]{4}\\.[0-9]{4}\\.[0-9]{2}$|^CHE-[0-9]{9}$|^756[0-9]{10}$" ]
        VCT.regex: [ "^[0-9]*$" ]
        SAU.regex: [ "^[0-9]{10}$" ]
        TWN.regex: [ "^[A-Z][0-9]{9}$" ]
        THA.regex: [ "^[0-9]{13}$" ]
        TJK.regex: [ "^[0-9]{9,10}$" ]
        TTO.regex: [ "^[0-9]{9}-[0-9]$" ]
        TUR.regex: [ "^[0-9]{10,11}$" ]
        GBR.regex: [ "^[0-9]{10}$|^(?!BG)(?!GB)(?!NK)(?!KN)(?!TN)(?!NT)(?!ZZ)(?:[A-CEGHJ-PR-TW-Z][A-CEGHJ-NPR-TW-Z])(?:\\s*\\d\\s*){6}([A-DFMP]|\\s)$" ]
        USA.regex: [ "^[A-Z0-9]{6}\\.[A-Z0-9]{5}\\.[A-Z]{2}\\.[0-9]{3}$|^[0-9]{9}$" ]
        VEN.regex: [ "^[A-Z]-[0-9]{8}-[0-9]$" ]
        URY.regex: [ "^[0-9]{8,9}$|^[0-9]{12}$" ]
        UZB.regex: [ "^[2-7][0-9]{8}$" ]

# case-change-analyser-service
sapphirus.diligence.case-change-analyser:
  enable-immediate-processing: true
  event-lanes:
    enabled: true
    lane-metadata-name: X-Lane

analyser:
  collection: sph_case-change-analyser_case-status
  artifact-collection: sph_case-change-analyser_data-artifacts
  case-change-task:
    rate: 60000
    max-case-age-in-seconds: 300


# case-sewing-service
case-sewing:
  publishResults: true
  checkIncomingArtifacts: true

# claimcheck-api-service
claim-check:
  expurgation-task:
    rate: 86400
    ttl-seconds: 0

# content-manager-service
sapphirus:
  rootFolderPath: /resources/sapphirus/
  mongo:
    collection: Document
  dispatcher:
    event-lanes:
      enabled: true
      lane-metadata-name: X-Lane
  diligence:
    dispatcher:
      event-lanes:
        enabled: true
        lane-metadata-name: X-Lane

dispatcher:
  user: kpmg-system
  context-collection: dispatcher-context
  request-collection: dispatcher-requests
  controller:
    max-cancel-request-guids: 1000000
    max-republish-request-guids: 1000000

# extraction-builder-key-value-service
sphbuilder.default.document.type: UNKNOWN_DOCUMENT_TYPE

# extraction-classifier-service
sapphirus.extraction.classifier:
  modelId: "GENERAL_CLASSIFICATION_MODEL_v1.2"
  thresholds:
    unknownDocumentType: "UNKNOWN_DOCUMENT_TYPE"
    global: 0.5
    local:
      - document_type: "STATEMENT_INCORPORATION_PT"
        value: 0.70
      - documentType: "UBO"
        value: 0.90
      - documentType: "UBO_LOOKUP"
        value: 0.70
    unknown_content_pages: 0.2
  azure:
    endpoint: https://url.com
    key: key

# extraction-extractor-form-recognizder-service
sapphirus.extraction.extractor:
  work-modes:
    service-requests:
      enabled: false
      service-codes:
        - DOCUMENT_UNSUPERVISED_EXTRACTION
    data-artifacts:
      enabled: true
      artifact-schema-code: DOCUMENT
      qc-status: PASSED

sapphirus.extraction.custom-model-extractor:
  extraction-mode:
    default-mode:
      mode: ONE_PAGE_AT_A_TIME
    document-type-modes:
      RCS_FR:
        mode: ALL_PAGES
      RCS_GER:
        mode: ALL_PAGES
  composed-models:
    firstPagesForMultiPageModels: firstPagesForMultiPageModels_v10.18
    otherPagesForMultiPageModels: otherPagesForMultiPageModels_v8.15
    firstPagesForSinglePageModels: firstPagesForSinglePageModels_v8.17
  artifactSchemaCode-composed-models:
    STATEMENT_OF_INCORPORATION_ES:
      multiPages: CPE_ES_v1.0
      singlePage: CPE_ES_v1.0
    STATEMENT_OF_INCORPORATION_NL:
      multiPages: CPE_NL_v1.0
      singlePage: CPE_NL_v1.0
    STATEMENT_OF_INCORPORATION_BE:
      multiPages: CPE_BE_v1.0
      singlePage: CPE_BE_v1.0
    IDCARD_PT:
      multiPages: IDCARD_PT_v1
      singlePage: IDCARD_PT_COMPLETE_v3_4
    IDCARD:
      multiPages: prebuilt-idDocument
      singlePage: prebuilt-idDocument
    IDCARD_ESP:
      multiPages: IDCARD_ESP_v1.1
      singlePage: IDCARD_ESP_COMPLETE_v2.1
    IDCARD_LUX:
      multiPages: IDCARD_LUX_v2.1
      singlePage: IDCARD_LUX_COMPLETE_v1.3
    PASSPORT_PT:
      multiPages: PASSPORT_PT_v3_6
      singlePage: PASSPORT_PT_v3_6
    PASSPORT_NON_PT:
      multiPages: PASSPORTS_NON_PT_v2
      singlePage: PASSPORTS_NON_PT_v2
    PASSPORT:
      multiPages: prebuilt-idDocument
      singlePage: prebuilt-idDocument
    KPMG_CLIENT_ONBOARDING_FORM:
      multiPages: KPMG_FORM_v1
      singlePage: KPMG_FORM_v1
    UBO_LOOKUP:
      multiPages: RCBE_CONSULT_v3.2
      singlePage: RCBE_CONSULT_v3.2
    STATEMENT_OF_INCORPORATION_PT:
      multiPages: STATEMENT_INCORPORATION_PT_multiPages_v1
      singlePage: STATEMENT_INCORPORATION_PT_singlePage_v1
    UBODECLARATION_PT:
      multiPages: RCBE_composed_v3.0
      singlePage: RCBE_composed_v3.0
    UBODECLARATION_DE:
      multiPages: RCBE_DE_v1.0
      singlePage: RCBE_DE_v1.0
    UBODECLARATION_NL:
      multiPages: RCBE_NL_v1.1
      singlePage: RCBE_NL_v1.1
    UBODECLARATION_IE:
      multiPages: RCBE_IE_v1.0
      singlePage: RCBE_IE_v1.0
    UBODECLARATION_IT:
      multiPages: RCBE_IT1_v1.0
      singlePage: RCBE_IT1_v1.0
    UBODECLARATION_BE:
      multiPages: RCBE_BE_v1.0
      singlePage: RCBE_BE_v1.0
    RBE:
      multiPages: RBE_v1
      singlePage: RBE_v1
    RBE_LUX:
      multiPages: RBE_LUX_v1.0
      singlePage: RBE_LUX_v1.0
    RBE_LUX2:
      multiPages: RBE_LUX2_v1.0
      singlePage: RBE_LUX2_v1.0
    RCS:
      multiPages: RCS_v3
      singlePage: RCS_v3
    RCS_LUX:
      multiPages: RCS_LUX_v1.0
      singlePage: RCS_LUX_v1.0
    RCS_LUX2:
      multiPages: RCS_LUX2_v2.1
      singlePage: RCS_LUX2_v2.1
    RCS_LUX3:
      multiPages: RCS_LUX3_v2.0
      singlePage: RCS_LUX3_v2.0
    RCS_FR:
      multiPages: RCS_FR_v0.13
      singlePage: RCS_FR_v0.13
    RCS_GER:
      multiPages: RCS_GER_v0.2
      singlePage: RCS_GER_v0.2
  model-groups:
    certidao-new-other-pages:
      modelId: STATEMENT_INCORPORATION_PT_NEW_OTHER_PAGES_composed_v1
      members:
        - STATEMENT_INCORPORATION_PT_NEW_OTHER_PAGE
        - STATEMENT_INCORPORATION_PT_NEW_OTHER_PAGE_AVERB
        - STATEMENT_INCORPORATION_PT_NEW_EXPIRY
    certidao-old-other-pages:
      modelId: STATEMENT_INCORPORATION_PT_OLD_composed_v1
      members:
        - STATEMENT_INCORPORATION_PT_OLD
        - STATEMENT_INCORPORATION_PT_OLD_EXPIRY
    RCBE:
      modelId: RCBE_composed_v3.0
      members:
        - RCBE_NORMAL
        - RCBE_EMAIL
        - RCBE_FORM
        - RCBE_EXPORT
    RCBE_NORMAL:
      modelId: RCBE_v3.2
      members:
        - RCBE
    RCBE_EMAIL:
      modelId: RCBE_EMAIL_v1n
      members:
        - RCBE_EMAIL
    RCBE_FORM:
      modelId: RCBE_FORM_v2.0n
      members:
        - RCBE_FORM
    RCBE_EXPORT:
      modelId: RCBE_EXPORT_v1n
      members:
        - RCBE_EXPORT
    RCBE_CONSULT:
      modelId: RCBE_CONSULT_v3.2
      members:
        - RCBE_CONSULT
    UBODECLARATION_DE:
      modelId: RCBE_DE_v1.0
      members:
        - UBODECLARATION_DE
    UBODECLARATION_NL:
      modelId: RCBE_NL_v1.1
      members:
        - UBODECLARATION_NL
    UBODECLARATION_IT:
      modelId: RCBE_IT1_v1.0
      members:
        - UBODECLARATION_IT
    UBODECLARATION_IE:
      modelId: RCBE_IE_v1.0
      members:
        - UBODECLARATION_IE
    UBODECLARATION_BE:
      modelId: RCBE_BE_v1.0
      members:
        - UBODECLARATION_BE
    IDCARD:
      modelId: IDCARD_PT_v1
      members:
        - IDCARD_PT_COMPLETE
        - IDCARD_PT_BACK
        - IDCARD_PT_FRONT
    IDCARD_ESP:
      modelId: IDCARD_ESP_v1.1
      members:
        -IDCARD_ESP_COMPLETE
        -IDCARD_ESP_BACK
        -IDCARD_ESP_FRONT
    IDCARD_LUX:
      modelId: IDCARD_LUX_v2.1
      members:
        -IDCARD_LUX_COMPLETE
        -IDCARD_LUX_BACK
        -IDCARD_LUX_FRONT
    KPMGFORM:
      modelId: KPMG_FORM_v1
      members:
        - KPMG_FORM_CLIENT_DATA
        - KPMG_FORM_FOUR_TABLES
        - KPMG_FORM_COLLECTIVE_PERSON
        - KPMG_FORM_INDIVIDUAL_PERSON
        - KPMG_FORM_BENEFICIARY
        - KPMG_FORM_BOARD_MEMBER
        - KPMG_FORM_OTHER_REPRESENTATIVES
    PASSPORT_PT:
      modelId: PASSPORT_PT_v3_6
      members:
        - PASSPORT_PT
    PASSPORT:
      modelId: prebuilt-idDocument
      members:
        - PASSPORT
    PASSPORT_FRA:
      modelId: PASSPORT_FRA_v2_5
      members:
        - PASSPORT_FRA
    PASSPORT_CH:
      modelId: PASSPORT_CH_v1
      members:
        - PASSPORT_CH
    PASSPORT_UK:
      modelId: PASSPORT_UK_v2_2
      members:
        - PASSPORT_UK
    PASSPORT_ITA:
      modelId: PASSPORT_ITA_v1
      members:
        - PASSPORT_ITA
    PASSPORT_SWE:
      modelId: PASSPORT_SWE_v1n
      members:
        - PASSPORT_SWE
    PASSPORT_ESP:
      modelId: PASSPORT_ESP_v1
      members:
        - PASSPORT_ESP
    STATEMENT_INCORPORATION_ES:
      modelId: CPE_ES_v1.0
      members:
        - STATEMENT_INCORPORATION_ES
    STATEMENT_INCORPORATION_NL:
      modelId: CPE_NL_v1.0
      members:
        - STATEMENT_INCORPORATION_NL
    RBE:
      modelId: RBE_v1
      members:
        - RBE
    RBE_LUX:
      modelId: RBE_LUX_v1.0
      members:
        - RBE_LUX
    RBE_LUX2:
      modelId: RBE_LUX2_v1.0
      members:
        - RBE_LUX2
    RCS:
      modelId: RCS_v3
      members:
        - RCS
    RCS_LUX:
      modelId: RCS_LUX_v1.0
      members:
        - RCS_LUX
    RCS_LUX2:
      modelId: RCS_LUX2_v2.1
      members:
        - RCS_LUX2
    RCS_LUX3:
      modelId: RCS_LUX3_v2.0
      members:
        - RCS_LUX3
    RCS_FR:
      modelId: RCS_FR_v0.13
      members:
        - RCS_FR
    RCS_GER:
      modelId: RCS_GER_v0.2
      members:
        - RCS_GER
    STATEMENT_INCORPORATION_BE:
      modelId: CPE_BE_v1.0
      members:
        - STATEMENT_INCORPORATION_BE
  models:
    STATEMENT_INCORPORATION_PT_NEW_FIRST_PAGE: STATEMENT_INCORPORATION_PT_NEW_FIRST_PAGE_v1n
    STATEMENT_INCORPORATION_PT_NEW_OTHER_PAGE: STATEMENT_INCORPORATION_PT_NEW_OTHER_PAGE_v1
    STATEMENT_INCORPORATION_PT_NEW_OTHER_PAGE_AVERB: STATEMENT_INCORPORATION_PT_NEW_OTHER_PAGE_AVERB_v1n
    STATEMENT_INCORPORATION_PT_NEW_EXPIRY: STATEMENT_INCORPORATION_PT_NEW_EXPIRY_v1
    STATEMENT_INCORPORATION_PT_OLD: STATEMENT_INCORPORATION_PT_OLD_v1
    STATEMENT_INCORPORATION_PT_OLD_EXPIRY: STATEMENT_INCORPORATION_PT_OLD_EXPIRY_v1
    STATEMENT_INCORPORATION_ES: CPE_ES_v1.0n
    STATEMENT_INCORPORATION_NL: CPE_NL_v1.0n
    STATEMENT_INCORPORATION_BE: CPE_BE_v1.0n
    RBE: RBE_v1
    RBE_LUX: RBE_LUX_v1.0n
    RBE_LUX2: RBE_LUX2_v1.0n
    RCS: RCS_v3
    RCS_LUX: RCS_LUX_v1.0
    RCS_LUX2: RCS_LUX2_v2.1n
    RCS_LUX3: RCS_LUX3_v2.0n
    RCS_FR: RCS_FR_v0.13
    RCS_GER: RCS_GER_v0.2
    RCBE: RCBE_v3.2
    UBODECLARATION_DE: RCBE_DE_v1.0n
    UBODECLARATION_IE: RCBE_IE_v1.0n
    UBODECLARATION_NL: RCBE_NL_v1.1n
    UBODECLARATION_IT: RCBE_IT1_v1.0n
    UBODECLARATION_BE: RCBE_BE_v1.0n
    RCBE_EMAIL: RCBE_EMAIL_v1n
    RCBE_FORM: RCBE_FORM_v2.0n
    RCBE_EXPORT: RCBE_EXPORT_v1n
    RCBE_CONSULT: RCBE_CONSULT_v3.2
    IDCARD_PT_FRONT: IDCARD_PT_FRONT_v1
    IDCARD_PT_BACK: IDCARD_PT_BACK_v1
    IDCARD_PT_COMPLETE: IDCARD_PT_COMPLETE_v3_4
    IDCARD_ESP_FRONT: IDCARD_ESP_FRONT_v2.0
    IDCARD_ESP_BACK: IDCARD_ESP_BACK_v2.1
    IDCARD_ESP_COMPLETE: IDCARD_ESP_COMPLETE_v2.1
    IDCARD_ESP_FRONT_NEW: IDCARD_ESP_FRONT_NEW_v1.0
    IDCARD_ESP_FRONT_OLD: IDCARD_ESP_FRONT_OLD_v1.1
    IDCARD_ESP_BACK_NEW: IDCARD_ESP_BACK_NEW_v1.3
    IDCARD_ESP_BACK_OLD: IDCARD_ESP_BACK_OLD_v1.2
    IDCARD_ESP_COMPLETE_NEW: IDCARD_ESP_COMPLETE_NEW_v1.3
    IDCARD_ESP_COMPLETE_OLD: IDCARD_ESP_COMPLETE_OLD_v1.2
    IDCARD_LUX_FRONT: IDCARD_LUX_FRONT_v2.4
    IDCARD_LUX_BACK: IDCARD_LUX_BACK_v2.0
    IDCARD_LUX_COMPLETE: IDCARD_LUX_COMPLETE_v1.3
    KPMG_FORM_CLIENT_DATA: KPMG_FORM_CLIENT_DATA_v1
    KPMG_FORM_FOUR_TABLES: KPMG_FORM_FOUR_TABLES_v1
    KPMG_FORM_COLLECTIVE_PERSON: KPMG_FORM_COLLECTIVE_PERSON_v1
    KPMG_FORM_INDIVIDUAL_PERSON: KPMG_FORM_INDIVIDUAL_PERSON_v1
    KPMG_FORM_BENEFICIARY: KPMG_FORM_BENEFICIARY_v1
    KPMG_FORM_BOARD_MEMBER: KPMG_FORM_BOARD_MEMBER_v1
    KPMG_FORM_OTHER_REPRESENTATIVES: KPMG_FORM_OTHER_REPRESENTATIVES_v1
    PASSPORT_PT: PASSPORT_PT_v3_6
    PASSPORT_FRA: PASSPORT_FRA_v2_5
    PASSPORT_CH: PASSPORT_CH_v1
    PASSPORT_UK: PASSPORT_UK_v2_2
    PASSPORT_ITA: PASSPORT_ITA_v1
    PASSPORT_SWE: PASSPORT_SWE_v1n
    PASSPORT_ESP: PASSPORT_ESP_v1
  postProcessing:
    KPMG_FORM_INDIVIDUAL_PERSON: pt.kpmg.sph.extractionextractor.service.processor.KpmgFormSelectionMarksPostProcessor
    KPMG_FORM_BENEFICIARY: pt.kpmg.sph.extractionextractor.service.processor.KpmgFormSelectionMarksPostProcessor
    KPMG_FORM_BOARD_MEMBER: pt.kpmg.sph.extractionextractor.service.processor.KpmgFormSelectionMarksPostProcessor
    KPMG_FORM_OTHER_REPRESENTATIVES: pt.kpmg.sph.extractionextractor.service.processor.KpmgFormSelectionMarksPostProcessor
    RCBE_CONSULT: pt.kpmg.sph.extraction.extractor.processor.RcbeLookupTaxIDNumberProcessor
    RBE_LUX: pt.kpmg.sph.extractionextractor.service.processor.RbeLuxSwapNameProcessor
    RBE_LUX2: pt.kpmg.sph.extractionextractor.service.processor.RbeLuxSwapNameProcessor
    CPE_BE: pt.kpmg.sph.extractionextractor.service.processor.CpeBeSwapNameProcessor
  rbeLuxExcludedNames:
    - "Name, first name(s)"
    - "Nom, Prénom(s)"
    - "Name, Vorname(en)"
  bigBoundingBox:
    - KPMGFORM
  azure:
    endpoint: https://ptazreuw-forms-dev-23077.cognitiveservices.azure.com
    key: key #507d572c168b4d1fa333c1f1e3c895a6 #${AZURE_KEY_CLOUD}
  columnsPattern: "^(COLUMN|column)[0-9]+$"
# extraction-manager-service
sapphirus.extraction.manager:
  work-modes:
    service-requests:
      enabled: true
      serviceCodes:
        - EXTRACTION_CODE
    data-artifacts:
      enabled: true
      artifact-schema-code: DOCUMENT
      qc-status: PASSED
    classified-artifacts:
      enabled: true
  verifiable-docs:
    - IDCARD_PT
    - IDCARD_ESP
    - IDCARD_LUX
    - PASSPORT
    - PASSPORT_PT
    - PASSPORT_NON_PT

# extraction-splitter-service
sapphirus.extraction.splitter:
  sequences:
    - documentCombination: UBODECLARATION_AND_LOOKUP_PT, UBODECLARATION_PT
      resultDocumentType: UBODECLARATION_AND_LOOKUP_PT
      ordered: true
    - documentCombination: IDCARD_PT_FRONT, IDCARD_PT_BACK
      resultDocumentType: IDCARD_PT
      ordered: false
      isStandaloneType: false
    - documentCombination: IDCARD_PT_COMPLETE
      resultDocumentType: IDCARD_PT
      ordered: false
      isStandaloneType: false
    - documentCombination: IDCARD_ESP_FRONT, IDCARD_ESP_BACK
      resultDocumentType: IDCARD_ESP
      ordered: false
      isStandaloneType: false
    - documentCombination: IDCARD_ESP_COMPLETE
      resultDocumentType: IDCARD_ESP
      ordered: false
      isStandaloneType: false
    - documentCombination: IDCARD_LUX_FRONT, IDCARD_LUX_BACK
      resultDocumentType: IDCARD_LUX
      ordered: false
      isStandaloneType: false
    - documentCombination: IDCARD_LUX_COMPLETE
      resultDocumentType: IDCARD_LUX
      ordered: false
      isStandaloneType: false
  nonContentPages:
    documentType: UNKNOWN_DOCUMENT_TYPE
    threshold: 0.2

# extraction-storage-service
sapphirus.extraction.storage:
  collection: extracted-contents
  mongo:
    use-grid-fs-storage: false

  # Case Storage
sapphirus.case-storage:
  mongo:
    use-grid-fs-storage: false
    auto-index-creation: true

# extraction-verifier-service
sapphirus.extraction-verifier:
  pending-result-retrieval:
    rate-in-millis: 15000
  event-lanes:
    enabled: true
    lane-metadata-name: X-Lane
  regula:
    verifiable-doc-types:
      - IDCARD_ESP
      - IDCARD_LUX
      - PASSPORT
      - PASSPORT_NON_PT
      - PASSPORT_PT
    license: abcdefghijklmnopqrstuvwxyz0123456789
    baseUrl: http://url.com
    authenticationParameters:
      # See more here:
      # https://docs.regulaforensics.com/develop/doc-reader-sdk/web-service/development/usage/authenticity/
      checkLiveness: false
      checkEd: false
      checkHolo: false
      checkMli: false
      checkOvi: false
      checkFibers: true
      checkExtMrz: true
      checkAxial: true
      checkImagePatterns: true
      checkLetterScreen: true
      checkBarcodeFormat: true
      checkExtOcr: true
      checkIpi: true
      checkPhotoComparison: true
      checkPhotoEmbedding: true
      checkIrVisibility: false
      checkIrb900: false
      checkUvLuminescence: false
    docTypeMapper:
      # See com.regula.documentreader.webclient.model.DocumentType for more types:
      # IDENTITY_CARD = 12
      # PASSPORT = 11
      IDCARD_PT: [ 12 ]
      IDCARD_ESP: [ 12 ]
      IDCARD_LUX: [ 12 ]
      PASSPORT: [ 11 ]
      PASSPORT_PT: [ 11 ]
      PASSPORT_NON_PT: [ 11 ]
  au10tix:
    verifiable-doc-types:
      - IDCARD_PT
    toleranceToRisk: medium # Possible values (case-insensitive): low, medium, high, ultrahigh
    doubleCheckDirective: none # Possible values (case-insensitive): none, senddirectly, skipsending
    clientId: abc123
    baseUrl: http://localhost:8080
    accessTokenUrl: http://localhost:8080
    jwtAudience: http://localhost:8080
    publicRsaKeyId: abc123
    privateRsaKey: abcdefghijklmnopqrstuvwxyz0123456789
    startIdVerificationWorkflowUrl: /foo/bar
    getAggregatedResultsBaseUrl: /foo/bar

# party-change-analyser-service
sapphirus.party-change-analyser:
  party-change-task:
    rate: 60000
    max-age-in-seconds: 300

  # policy-service
  # Policy Service
policy-service:
  use-grid-fs-storage: false

sphconfig:
  collections:
    artifact-schemas: sph_sphconfig_artifact-schemas
    ontologies: sph_sphconfig_ontologies
    ontology-versions: sph_sphconfig_ontology_versions
    global-enums: sph_sphconfig_global-enums
    database-sequence: sph_sphconfig_ontology_database-sequences
hash.validator.status: false

# recycling-service
recycler:
  recycleRequester:
    serviceCodes:
      - RECYCLE_DOCS

# timeout-controller-service
sapphirus.diligence.timeout-controller:
  collection: sph_timeout-controller
  scheduler-task-rate-ms: 1000

party-manager:
  graph:
    default:
      enable: false
    neo4j:
      enable: true

# fragment-stylist-service properties
sph.fragment-stylist:
  greedy: false
  useEmptyAttributeValues: false