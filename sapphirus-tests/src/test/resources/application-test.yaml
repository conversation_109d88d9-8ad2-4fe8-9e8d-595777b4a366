spring:
  config.import: classpath:form-extractor-properties-local.yml,classpath:builder-mappings-kv-local.yaml,classpath:application-extractor.yml
  servlet:
    multipart:
      enabled: true
  cloud:
    function:
      # TODO: still missing artifact assembler
      # TODO: Refactor recycling's consumers and suppliers to functions.
      definition: >
        ${base-test.function.definition};
        artifactApiServiceIngestionArtifactConsumer;
        artifactApiServiceIngestionCaseFileConsumer;
        artifactApiServiceFinalArtifactSupplier;
        artifactApiServiceDataArtifactConsumer;
        artifactApiServiceDataArtifactSupplier;
        artifactApiServiceCaseFileConsumer;
        artifactApiServiceOutreachStatusSupplier;
        artifactApiServiceOutreachRequestConsumer;
        artifactAssemblerServiceAssembledArtifactFunction;
        artifactQualityServiceDataArtifactConsumer;
        artifactQualityServiceDataArtifactSupplier;
        caseChangeAnalyserServiceCaseFileConsumer;
        caseChangeAnalyserServiceDataArtifactConsumer;
        caseChangeAnalyserServiceOutreachStatusSupplier;
        caseReviewServiceCaseFileFunction;
        caseSewingServiceCaseFragmentsConsumer;
        caseSewingServiceBlueprintCaseFilesSupplier;
        caseSewingServiceDataArtifactConsumer;
        caseStorageServiceCaseFileFunction;
        caseStylistServiceCaseFileFunction;
        contentManagerServiceDocumentActionsSupplier;
        extractionVerifierServiceKafkaConsumerFunction;
        extractionVerifierServiceVerificationResponseSupplier;
        dispatcherRequestStatusConsumer;
        dispatcherRequestServiceSupplier;
        dispatcherIngestionCaseSupplier;
        dispatcherIngestionArtifactsSupplier;
        extractionClassifierClassificationRequestFunction;
        extractionClassifierApiClassificationResultSupplier;
        extractionClassifierClassificationResponseConsumer;
        extractionSplitterClassifiedArtifactSupplier;
        extractionBuilderServiceExtractionFunction;
        extractionExtractorExtractionRequestConsumer;
        extractionExtractorApiPageExtractionResultSupplier;
        extractionExtractorExtractionResultSupplier;
        extractionManagerServiceServiceRequestsFunction;
        extractionManagerServiceDataArtifactsFunction;
        extractionManagerServiceClassifiedArtifactsFunction;
        extractionManagerServiceAssembledArtifactsConsumer;
        extractionManagerServiceVerificationResponsesFunction;
        extractionManagerServiceDataArtifactsSupplier;
        extractionManagerServiceExtractionRequestsSupplier;
        extractionManagerServiceVerificationRequestsSupplier;
        extractionManagerServiceServiceStatusSupplier;
        fragmentStylistServiceDataArtifactConsumer;
        fragmentStylistServiceCaseArtifactSupplier;
        managementInformationServiceCaseFileConsumer;
        managementInformationServiceExtractionBuildResultConsumer;
        managementInformationServiceDocumentActionConsumer;
        managementInformationServiceDataArtifactConsumer;
        managementInformationServiceAssembledArtifactConsumer;
        managementInformationServiceClassificationRequestConsumer;
        managementInformationServiceClassificationResponseConsumer;
        managementInformationServiceApiClassificationResponseConsumer;
        managementInformationServiceClassifiedArtifactConsumer;
        managementInformationServiceExtractionRequestConsumer;
        managementInformationServiceExtractionResultConsumer;
        managementInformationServiceOutreachRequestConsumer;
        managementInformationServiceOutreachStatusConsumer;
        managementInformationServiceApiPageExtractionResultConsumer;
        managementInformationServiceTaskStatusConsumer;
        managementInformationServiceVerificationRequestConsumer;
        managementInformationServiceVerificationResponseConsumer;
        partyManagerServiceCaseFilesConsumer;
        partyManagerServiceCaseReportConsumer;
        partyManagerServiceCaseReportSupplier;
        partyManagerServicePartyCaseReportsConsumer;
        partyManagerServicePartyCaseReportsSupplier;
        partyManagerServicePartySupplier;
        timeoutControllerServiceOutreachRequestConsumer;
        timeoutControllerServiceOutreachStatusConsumer;
        timeoutControllerServiceOutreachStatusSupplier;
        artifactRecyclingServiceOutreachRequestsConsumer;
        artifactRecyclingServiceDataArtifactsConsumer;
        artifactRecyclingServiceIndexRequestsConsumer;
        artifactRecyclingServiceIndexedArtifactsConsumer;
        artifactRecyclingServiceRecycleRequestsConsumer;
        artifactRecyclingServiceIndexRequestsSupplier;
        artifactRecyclingServiceIndexedArtifactsSupplier;
        artifactRecyclingServiceRecycleRequestsSupplier;
        artifactRecyclingServiceOutreachStatusSupplier;
        partyChangeAnalyserServicePartyConsumer;
        partyChangeAnalyserServicePartySupplier;
        partyDispatcherPartyConsumer;
        tenantManagementTenantSupplier;
        auditEntryRequestConsumer;

    stream:
      bindings:
        # artifact-api-service
        artifactApiServiceOutreachStatusSupplier-out-0:
          destination: ${kafka.topics.sapphirus.diligence.service-status}
        artifactApiServiceOutreachRequestConsumer-in-0:
          group: sph_artifact_api_service
          destination: ${kafka.topics.sapphirus.diligence.service-requests}
        artifactApiServiceIngestionArtifactConsumer-in-0:
          group: sph_artifact_api_service
          destination: ${kafka.topics.sapphirus.ingestion.ingestion-artifacts}
        artifactApiServiceIngestionCaseFileConsumer-in-0:
          group: sph_artifact_api_service
          destination: ${kafka.topics.sapphirus.ingestion.ingestion-case-files}
        artifactApiServiceDataArtifactConsumer-in-0:
          group: sph_artifact_api_service
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}
        artifactApiServiceDataArtifactSupplier-out-0:
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}
        artifactApiServiceFinalArtifactSupplier-out-0:
          destination: ${kafka.topics.sapphirus.ingestion.processing-artifacts}
        artifactApiServiceCaseFileConsumer-in-0:
          group: sph_artifact_api_service
          destination: ${kafka.topics.sapphirus.review.case-files}

        # artifact-assembler-service
        artifactAssemblerServiceAssembledArtifactFunction-in-0:
          group: sph-artifact-assembler
          destination: ${kafka.topics.sapphirus.extraction.built-artifacts}
        artifactAssemblerServiceAssembledArtifactFunction-out-0:
          group: sph-artifact-assembler
          destination: ${kafka.topics.sapphirus.extraction.assembled-artifacts}

        # artifact-quality-service
        artifactQualityServiceDataArtifactConsumer-in-0:
          group: sapphirus-artifact-quality
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}
        artifactQualityServiceDataArtifactSupplier-out-0:
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}

        # case-change-analyser-service
        caseChangeAnalyserServiceCaseFileConsumer-in-0:
          group: sph_case_change_analyser
          destination: ${kafka.topics.sapphirus.review.case-files}
        caseChangeAnalyserServiceDataArtifactConsumer-in-0:
          group: sph_case_change_analyser
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}
        caseChangeAnalyserServiceOutreachStatusSupplier-out-0:
          destination: ${kafka.topics.sapphirus.diligence.service-status}

        # case-review-service
        caseReviewServiceCaseFileFunction-in-0:
          group: sph_case_review
          destination: ${kafka.topics.sapphirus.assembly.case-enhancements}
        caseReviewServiceCaseFileFunction-out-0:
          destination: ${kafka.topics.sapphirus.review.case-revisions}

        # case-sewing-service
        caseSewingServiceCaseFragmentsConsumer-in-0:
          group: case-sewing
          destination: ${kafka.topics.sapphirus.ingestion.case-fragments}
        caseSewingServiceBlueprintCaseFilesSupplier-out-0:
          destination: ${kafka.topics.sapphirus.assembly.case-sketches}
        caseSewingServiceDataArtifactConsumer-in-0:
          group: case-sewing
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}

        # case-storage-service
        caseStorageServiceCaseFileFunction-in-0:
          group: case-storage
          destination: ${kafka.topics.sapphirus.review.case-revisions}
        caseStorageServiceCaseFileFunction-out-0:
          destination: ${kafka.topics.sapphirus.review.case-files}

        # case-stylist-service
        caseStylistServiceCaseFileFunction-in-0:
          group: sph_case_stylist
          destination: ${kafka.topics.sapphirus.assembly.case-sketches}
        caseStylistServiceCaseFileFunction-out-0:
          destination: ${kafka.topics.sapphirus.assembly.case-enhancements}

        # content-manager-service
        contentManagerServiceDocumentActionsSupplier-out-0:
          destination: ${kafka.topics.sapphirus.content-manager.document-actions}

        # extraction-verifier-service
        extractionVerifierServiceKafkaConsumerFunction-in-0:
          group: extraction-verifier
          destination: ${kafka.topics.sapphirus.extraction.verification-requests}
        extractionVerifierServiceKafkaConsumerFunction-out-0:
          destination: ${kafka.topics.sapphirus.extraction.verification-responses}
        extractionVerifierServiceVerificationResponseSupplier-out-0:
          destination: ${kafka.topics.sapphirus.extraction.verification-responses}

        # dispatcher-starter
        dispatcherRequestStatusConsumer-in-0:
          group: dispatcher
          destination: ${kafka.topics.sapphirus.diligence.service-status}
        dispatcherRequestServiceSupplier-out-0:
          destination: ${kafka.topics.sapphirus.diligence.service-requests}
        dispatcherIngestionCaseSupplier-out-0:
          destination: ${kafka.topics.sapphirus.ingestion.ingestion-case-files}
        dispatcherIngestionArtifactsSupplier-out-0:
          destination: ${kafka.topics.sapphirus.ingestion.ingestion-artifacts}

        # key-value-artifact-builder-service
        extractionBuilderServiceExtractionFunction-in-0:
          group: extraction-test-group
          destination: ${kafka.topics.sapphirus.extraction.extraction-results}
        extractionBuilderServiceExtractionFunction-out-0:
          destination: ${kafka.topics.sapphirus.extraction.built-artifacts}

        # extraction-classifier-service
        extractionClassifierClassificationRequestFunction-in-0:
          group: classifier
          destination: ${kafka.topics.sapphirus.extraction.classification-requests}
        extractionClassifierClassificationRequestFunction-out-0:
          destination: ${kafka.topics.sapphirus.extraction.classification-responses}
        extractionClassifierApiClassificationResultSupplier-out-0:
          destination: ${kafka.topics.sapphirus.extraction.api-classification-results}

        #extraction-splitter-service
        extractionClassifierClassificationResponseConsumer-in-0:
          destination: ${kafka.topics.sapphirus.extraction.classification-responses}
        extractionSplitterClassifiedArtifactSupplier-out-0:
          destination: ${kafka.topics.sapphirus.extraction.classified-artifacts}

        # form-recognizer-extraction-extractor-service
        extractionExtractorExtractionResultSupplier-out-0:
          destination: ${kafka.topics.sapphirus.extraction.extraction-results}
        extractionExtractorApiPageExtractionResultSupplier-out-0:
          destination: ${kafka.topics.sapphirus.extraction.page-extraction-results}
        extractionExtractorExtractionRequestConsumer-in-0:
          group: extractor
          destination: ${kafka.topics.sapphirus.extraction.extraction-requests}

        # extraction-manager
        extractionManagerServiceServiceRequestsFunction-in-0:
          group: extraction-manager
          destination: ${kafka.topics.sapphirus.diligence.service-requests}
        extractionManagerServiceServiceRequestsFunction-out-0:
          destination: ${kafka.topics.sapphirus.extraction.classification-requests}

        extractionManagerServiceDataArtifactsFunction-in-0:
          group: extraction-manager
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}
        extractionManagerServiceDataArtifactsFunction-out-0:
          destination: ${kafka.topics.sapphirus.extraction.classification-requests}

        extractionManagerServiceClassifiedArtifactsFunction-in-0:
          group: extraction-manager
          destination: ${kafka.topics.sapphirus.extraction.classified-artifacts}
        extractionManagerServiceClassifiedArtifactsFunction-out-0:
          destination: ${kafka.topics.sapphirus.extraction.extraction-requests}

        extractionManagerServiceAssembledArtifactsConsumer-in-0:
          group: extraction-manager
          destination: ${kafka.topics.sapphirus.extraction.assembled-artifacts}

        extractionManagerServiceVerificationResponsesFunction-in-0:
          group: extraction-manager
          destination: ${kafka.topics.sapphirus.extraction.verification-responses}
        extractionManagerServiceVerificationResponsesFunction-out-0:
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}

        extractionManagerServiceDataArtifactsSupplier-out-0:
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}
        extractionManagerServiceExtractionRequestsSupplier-out-0:
          destination: ${kafka.topics.sapphirus.extraction.extraction-requests}
        extractionManagerServiceVerificationRequestsSupplier-out-0:
          destination: ${kafka.topics.sapphirus.extraction.verification-requests}
        extractionManagerServiceServiceStatusSupplier-out-0:
          destination: ${kafka.topics.sapphirus.diligence.service-status}

        # fragment-stylist-service
        fragmentStylistServiceDataArtifactConsumer-in-0:
          group: sph_fragment_stylist
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}
        fragmentStylistServiceCaseArtifactSupplier-out-0:
          destination: ${kafka.topics.sapphirus.ingestion.case-fragments}

        # management-information-service
        managementInformationServiceCaseFileConsumer-in-0:
          destination: ${kafka.topics.sapphirus.review.case-files}
          group: sph_management_information
        managementInformationServiceTaskStatusConsumer-in-0:
          destination: ${kafka.topics.sapphirus.tasks.task-status}
          group: sph_management_information
        managementInformationServiceOutreachRequestConsumer-in-0:
          destination: ${kafka.topics.sapphirus.diligence.service-requests}
          group: sph_management_information
        managementInformationServiceOutreachStatusConsumer-in-0:
          destination: ${kafka.topics.sapphirus.diligence.service-status}
          group: sph_management_information
        managementInformationServiceDataArtifactConsumer-in-0:
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}
          group: sph_management_information
        managementInformationServiceClassificationRequestConsumer-in-0:
          destination: ${kafka.topics.sapphirus.extraction.classification-requests}
          group: sph_management_information
        managementInformationServiceClassificationResponseConsumer-in-0:
          destination: ${kafka.topics.sapphirus.extraction.classification-responses}
          group: sph_management_information
        managementInformationServiceApiClassificationResponseConsumer-in-0:
          destination: ${kafka.topics.sapphirus.extraction.api-classification-results}
          group: sph_management_information
        managementInformationServiceClassifiedArtifactConsumer-in-0:
          destination: ${kafka.topics.sapphirus.extraction.classified-artifacts}
          group: sph_management_information
        managementInformationServiceExtractionRequestConsumer-in-0:
          destination: ${kafka.topics.sapphirus.extraction.extraction-requests}
          group: sph_management_information
        managementInformationServiceApiPageExtractionResultConsumer-in-0:
          destination: ${kafka.topics.sapphirus.extraction.page-extraction-results}
          group: sph_management_information
        managementInformationServiceExtractionResultConsumer-in-0:
          destination: ${kafka.topics.sapphirus.extraction.extraction-results}
          group: sph_management_information
        managementInformationServiceExtractionBuildResultConsumer-in-0:
          destination: ${kafka.topics.sapphirus.extraction.built-artifacts}
          group: sph_management_information
        managementInformationServiceAssembledArtifactConsumer-in-0:
          destination: ${kafka.topics.sapphirus.extraction.assembled-artifacts}
          group: sph_management_information
        managementInformationServiceDocumentActionConsumer-in-0:
          destination: ${kafka.topics.sapphirus.content-manager.document-actions}
          group: sph_management_information
        managementInformationServiceVerificationRequestConsumer-in-0:
          destination: ${kafka.topics.sapphirus.extraction.verification-requests}
          group: sph_management_information
        managementInformationServiceVerificationResponseConsumer-in-0:
          destination: ${kafka.topics.sapphirus.extraction.verification-responses}
          group: sph_management_information

        # party-manager-service
        partyManagerServiceCaseFilesConsumer-in-0:
          group: sph_party_manager
          destination: ${kafka.topics.sapphirus.review.case-files}
        partyManagerServicePartyCaseReportsConsumer-in-0:
          group: sph_party_manager
          destination: ${kafka.topics.sapphirus.party-manager.party-case-reports}

        partyManagerServicePartyCaseReportsSupplier-out-0:
          destination: ${kafka.topics.sapphirus.party-manager.party-case-reports}
        partyManagerServicePartySupplier-out-0:
          destination: ${kafka.topics.sapphirus.party-manager.party}
        partyManagerServiceCaseReportConsumer-in-0:
          destination: ${kafka.topics.sapphirus.party-manager.case-reports}
        partyManagerServiceCaseReportSupplier-out-0:
          destination: ${kafka.topics.sapphirus.party-manager.case-reports}

        # timeout-controller-service
        timeoutControllerServiceOutreachRequestConsumer-in-0:
          group: sph_timeout_controller
          destination: ${kafka.topics.sapphirus.diligence.service-requests}
        timeoutControllerServiceOutreachStatusConsumer-in-0:
          group: sph_timeout_controller
          destination: ${kafka.topics.sapphirus.diligence.service-status}
        timeoutControllerServiceOutreachStatusSupplier-out-0:
          destination: ${kafka.topics.sapphirus.diligence.service-status}

        # recycling-service
        artifactRecyclingServiceOutreachRequestsConsumer-in-0:
          group: sph_artifact_recycling
          destination: ${kafka.topics.sapphirus.diligence.service-requests}
        artifactRecyclingServiceDataArtifactsConsumer-in-0:
          group: sph_artifact_recycling
          destination: ${kafka.topics.sapphirus.ingestion.data-artifacts}
        artifactRecyclingServiceIndexRequestsConsumer-in-0:
          group: sph_artifact_recycling
          destination: ${kafka.topics.sapphirus.recycling.index-requests}
        artifactRecyclingServiceIndexedArtifactsConsumer-in-0:
          group: sph_artifact_recycling
          destination: ${kafka.topics.sapphirus.recycling.indexed-artifacts}
        artifactRecyclingServiceRecycleRequestsConsumer-in-0:
          group: sph_artifact_recycling
          destination: ${kafka.topics.sapphirus.recycling.recycle-requests}
        artifactRecyclingServiceIndexRequestsSupplier-out-0:
          destination: ${kafka.topics.sapphirus.recycling.index-requests}
        artifactRecyclingServiceIndexedArtifactsSupplier-out-0:
          destination: ${kafka.topics.sapphirus.recycling.indexed-artifacts}
        artifactRecyclingServiceRecycleRequestsSupplier-out-0:
          destination: ${kafka.topics.sapphirus.recycling.recycle-requests}
        artifactRecyclingServiceOutreachStatusSupplier-out-0:
          destination: ${kafka.topics.sapphirus.diligence.service-status}

        # party-change-analyser-service
        partyChangeAnalyserServicePartyConsumer-in-0:
          group: case-change-analyser
          destination: ${kafka.topics.sapphirus.party-manager.party}
        partyChangeAnalyserServicePartySupplier-out-0:
          destination: ${kafka.topics.sapphirus.party-manager.party-updates}

        # party-dispatcher-starter
        partyDispatcherPartyConsumer-in-0:
          group: party-dispatcher
          destination: ${kafka.topics.sapphirus.party-manager.party-updates}

        #tenant-management
        tenantManagementTenantSupplier-out-0:
          destination: ${kafka.topics.sapphirus.tenant-management.tenant-updates}

        auditEntryRequestConsumer-in-0:
          group: audit-entries
          destination: ${kafka.topics.sapphirus.audit.entry-requests}
  data:
    mongodb:
      gridfs:
        database: test
  neo4j:
    uri: http://localhost:8080
    authentication:
      username: username
      password: password

graphql:
  servlet:
    mapping: /graphql
    enabled: true
    corsEnabled: true

openapi:
  caseChangeAnalyser.base-path: /case-change-analyser
  extractionStorage.base-path: /extraction-storage
  contentManager.base-path: /content-manager
  policyArtifactSchemas.base-path: /policy
  policyFiles.base-path: /policy
  policyEnumGroups.base-path: /policy
  policyOntologies.base-path: /policy
  policyRuntimeOntologies.base-path: /policy
  artifactAdmin.base-path: /artifact-api
  artifactManagement.base-path: /artifact-api
  extractionManager.base-path: /extraction-manager
  caseStorage.base-path: /case-storage
  dispatcher.base-path: /dispatcher
  artifactQuality.base-path: /artifact-quality
  party.base-path: /party-manager
  claimcheck.base-path: /claimcheck-api
  sPHArtifactRecycling.base-path: /artifact-recycling
  extractionVerifier.base-path: /extraction-verifier
  tenantManagement.base-path: /tenant-management

# common
sapphirus.common:
  expression:
    max-length: 30000
    max-cached-entries: 10000

# multitenancy
sapphirus.multitenancy:
  enabled: true

#sapphirus configuration manager
sapphirus.configuration-manager:
  storage.fileshare:
    base-folder-path: src/test/resources/files/configuration-manager/storage
  jpa:
    main-branch-name: main
    main-branch-description: Main branch description
    main-tag-name: MAIN_BASE_TAG
    main-tag-description: Main tag description

sapphirus.event-lanes:
  enabled: false
  lanes: [ ]
  kafka:
    enabled: false
    lane-event-header: X-Lane
    default-supplier-lane: Gold
    consumers: [ ]
    suppliers: [ ]
    functions: [ ]
  rest:
    enabled: true
    default-api-lane: Default-Lane
    lane-api-header: X-Api-Lane
    inbound-interceptor:
      enabled: true
    outbound-interceptor:
      enabled: true
    feign-outbound-interceptor:
      enabled: true
  enrichers:
    enabled: true

# artifact-api
sapphirus.ingestion:
  artifact-api:
    service-accounts:
      - service-account-kyc
      - service-account-postman
      - service-account-sapphirus
    event-lanes:
      enabled: false
      lane-metadata-name: X-Lane
    artifact-expiration-task:
      cron: "*/2 * * * * *" # execute every two seconds
    use-processing-topic: true

# artifact-quality
sapphirus.artifact-quality:
  tax-id-number-validations:
    rules:
      INDIVIDUAL:
        ALB.regex: [ "^([0-9A-T])[0-9]{8}[A-W]$" ]
        AND.regex: [ "^[FE]-[0-9]{6}-[A-Z]$|^[FE][0-9]{6}[A-Z]$" ]
        ARM.regex: [ "^[0-9]{10}$|[1-9][0-9]{7}" ]
        ARG.regex: [ "^[0-9]{11}$" ]
        AUS.regex: [ "^[0-9]{8,9}$" ]
        AUT.regex: [ "^[0-9]{2}-[0-9]{3}\\/[0-9]{4}$|^[0-9]{9}$" ]
        ABW.regex: [ "^[0-9]{7,8}$" ]
        AZE.regex: [ "^[0-9]{9}[2]$|^[0-9A-Z]{7}$" ]
        BLZ.regex: [ "^[0-9]{6}(10)?$" ]
        BEL.regex: [ "^[0-9]{11}$" ]
        BRA.regex: [ "^[0-9]{11}$" ]
        BGR.regex: [ "^[0-9]{10}$" ]
        BES.regex: [ "^[356][78][0-9]{5}[1-9][0-9]$" ]
        BLR.regex: [ "^[A-Z]{2}[0-9]{7}$" ]
        CAN.regex: [ "^[0-9]{9}$" ]
        CHL.regex: [ "^[0-9]{1,2}\\.[0-9]{3}\\.[0-9]{3}-[0-9K]$" ]
        CHN.regex: [ "^[0-9]{17}[0-9X]$|^[CWHMT][0-9]{16}[0-9A-Z]$|^J[0-9]{14}$" ]
        COL.regex: [ "^[0-9]{8}$|^[0-9]{10}$" ]
        CRI.regex: [ "^[0-9]-[0-9]{4}-[0-9]{4}$|^[0-9]{11}$" ]
        HRV.regex: [ "^[0-9]{11}$" ]
        CUW.regex: [ "^1[78][0-9]{5}[1-9][0-9]$" ]
        CYP.regex: [ "^[0-9]{8}[A-Z]$" ]
        CZE.regex: [ "^[0-9]{9,10}$" ]
        DNK.regex: [ "^[0-9]{6}-[0-9]{4}$" ]
        EST.regex: [ "^[1-6][0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])[0-9]{4}$" ]
        ECU.regex: [ "^[0-9]{13}$" ]
        FRO.regex: [ "^[0-9]{6}-?[0-9]{3}$" ]
        FIN.regex: [ "^[0-9]{6}[-\\+A][0-9]{3}[0-9A-Z]$" ]
        FRA.regex: [ "^[0-3][0-9]{12}$|^[0-9]{1,5}[A-Z]$" ]
        DEU.regex: [ "^[0-9]{11,12}$" ]
        GIB.regex: [ "^[0-9]{1,6}$" ]
        GRC.regex: [ "^[0-9]{9}$" ]
        GRL.regex: [ "^[0-9]{6}-?[0-9]{4}$" ]
        GGY.regex: [ "^GY[0-9]{6}$|^[A-Z]{2}[0-9]{6}[A-DFMP]$|^(JY[0-9]{6}[A-Z])$|^[0-9][A-Z]{1,2}[0-9]{6}B{0,1}$" ]
        HKG.regex: [ "^[A-Z]{1,2}[0-9]{6}[0-9A]$" ]
        HUN.regex: [ "^[1-8][0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])[0-9]{4}$|^8[0-9]{9}$" ]
        ISL.regex: [ "^[0-9]{6}-?[0-9]{4}$" ]
        IND.regex: [ "^[A-Z]{3}P[A-Z][0-9]{4}[A-Z]$" ]
        IMN.regex: [ "^(H[0-9]{6}(-[0-9]{2})?)$|^[A-Z]{2}[0-9]{6}[A-DFMP]$" ]
        IDN.regex: [ "^[0-9]{2}\\.[0-9]{3}\\.[0-9]{3}\\.[0-9]-[0-9]{3}\\.[0-9]{3}$|^[0-9]{15}$" ]
        IRL.regex: [ "^[0-9]{7}[A-Z]{1,2}$" ]
        ISR.regex: [ "^[0-9]{9}$" ]
        ITA.regex: [ "^[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]$" ]
        JAM.regex: [ "^[0-9]{9}$" ]
        JPN.regex: [ "^[0-9]{12}$" ]
        JEY.regex: [ "^[0-9]{10}$|^[A-Z]{2}[0-9]{6}[A-DFMP]$" ]
        KWT.regex: [ "^[0-9]*$" ]
        KEN.regex: [ "^[0-9]{11}$" ]
        KGZ.regex: [ "^[1-2](0[1-9]|[12][0-9]|3[01])(0[1-9]|1[0-2])(19[0-9]{2}|20[0-9]{2})[0-9]{6}$" ]
        KOR.regex: [ "^[0-9]{6}-[0-9]{7}$" ]
        KAZ.regex: [ "^[0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[1|2][0-9]|3[0|1])[0-9]{6}$" ]
        LBN.regex: [ "^[0-9]+$" ]
        LVA.regex: [ "^[0-9]{11}$" ]
        LIE.regex: [ "^[0-9]{1,12}$" ]
        LTU.regex: [ "^[0-9]{11}$" ]
        LUX.regex: [ "^[0-9]{13}$" ]
        MDA.regex: [ "^2[0-9]{12}$" ]
        MAF.regex: [ "^[0-9]{1,5}[A-Z]$" ]
        MAC.regex: [ "^0[0-9]{7}$" ]
        MYS.regex: [ "^([SO]G)\\s[0-9]{1,11}$" ]
        MLT.regex: [ "^[0-9]{3,7}[MGAPLHBZ]$|^[0-9]{9}$" ]
        MHL.regex: [ "^([0-9]{4}-04)$|^(04-[0-9]{4})$|^[0-9]{7,8}$" ]
        MUS.regex: [ "^[178][0-9]{7}$" ]
        MEX.regex: [ "^[A-Z]{4}[0-9]{6}[A-Z0-9]{3}$" ]
        NRU.regex: [ "^[0-9]{2}-[0-9]{5}-[0-9]-[0-9]$" ]
        NLD.regex: [ "^[0-9]{9}$" ]
        NZL.regex: [ "^[0-9]{8,9}$" ]
        NOR.regex: [ "^[0-9]{11}$" ]
        NGA.regex: [ "^[0-9]{8}$" ]
        PAK.regex: [ "^[0-9]{13}$" ]
        PER.regex: [ "^1[05][0-9]{9}$|^[0-9]{8}$" ]
        PAN.regex: [ "^[0-9]{5}-[0-9]{2}-[0-9]{6}$|^[0-9]{13}$" ]
        PRY.regex: [ "^[A-Z]{4}-[0-9]{6}[A-Z0-9]$" ]
        PHL.regex: [ "^[0-9]{9}$" ]
        POL.regex: [ "^[0-9]{11}$" ]
        PRT.regex: [ "^([1-3][0-9]{8})$|^(45[0-9]{7})$" ]
        ROU.regex: [ "^[1-9][0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[1|2][0-9]|3[0|1])(0[1-9]|[1-3][0-9]|4[0-7]|51|52)[0-9]{4}$|^90{3}[0-9]{8}$" ]
        RUS.regex: [ "^[0-9]{12}$" ]
        LCA.regex: [ "^[0-9]{1,6}$" ]
        SMR.regex: [ "^[0-9]{1,9}$" ]
        SYC.regex: [ "^[0-9]{2}2[0-9]{6}$" ]
        SGP.regex: [ "^[STFG][0-9]{7}[A-Z]$" ]
        SVK.regex: [ "^[0-9]{9,10}$" ]
        SVN.regex: [ "^[1-9][0-9]{7}$" ]
        SXM.regex: [ "^4[78][0-9]{5}[1-9][0-9]$" ]
        ZAF.regex: [ "^[01239][0-9]{3}\\/[0-9]{3}\\/[0-9]{3}$" ]
        ESP.regex: [ "^[0-9LMKXYZ][0-9]{7}[A-Z]$" ]
        SWE.regex: [ "^(19|20)\\d{10}$" ]
        CHE.regex: [ "^756\\.[0-9]{4}\\.[0-9]{4}\\.[0-9]{2}$|^756[0-9]{10}$" ]
        VCT.regex: [ "^[0-9]*$" ]
        SAU.regex: [ "^[0-9]{10}$" ]
        TWN.regex: [ "^[A-Z][0-9]{9}$" ]
        THA.regex: [ "^[0-9]{13}$" ]
        TJK.regex: [ "^[0-9]{2}[5-9][0-9]{6}$" ]
        TTO.regex: [ "^[0-9]{9}-[0-9]$" ]
        TUR.regex: [ "^[0-9]{10,11}$" ]
        GBR.regex: [ "^[0-9]{10}$|^(?!BG)(?!GB)(?!NK)(?!KN)(?!TN)(?!NT)(?!ZZ)(?:[A-CEGHJ-PR-TW-Z][A-CEGHJ-NPR-TW-Z])(?:\\s*\\d\\s*){6}([A-DFMP]|\\s)$" ]
        USA.regex: [ "^[0-9]{9}$" ]
        VEN.regex: [ "^[A-Z]-[0-9]{8}-[0-9]$" ]
        URY.regex: [ "^[0-9]{8,9}$" ]
        UZB.regex: [ "^[4-7][0-9]{8}$" ]
      COMPANY:
        ALB.regex: [ "^([0-9A-T])[0-9]{8}[A-W]$" ]
        AND.regex: [ "^[FEALCDGOPU]-[0-9]{6}-[A-Z]$|^[FEALCDGOPU][0-9]{6}[A-Z]$" ]
        ARM.regex: [ "^[0-9]{10}$|^[0-9]{8}$" ]
        ARG.regex: [ "^[0-9]{11}$" ]
        AUS.regex: [ "^[0-9]{8,9}$|^[0-9]{11}$" ]
        AUT.regex: [ "^[0-9]{2}-[0-9]{3}\\/[0-9]{4}$|^[0-9]{9}$" ]
        ABW.regex: [ "^[0-9]{7,8}$" ]
        AZE.regex: [ "^[0-9]{9}[1-2]$|^[0-9A-Z]{7}$" ]
        BLZ.regex: [ "^[0-9]{6}(10|13|66)?$" ]
        BEL.regex: [ "^[0-9]{10,12}$" ]
        BRA.regex: [ "^[0-9]{11}$|^[0-9]{2}\\.[0-9]{3}\\.[0-9]{3}\\/[0-9]{4}-[0-9]{2}$|^[0-9]{14}$" ]
        BGR.regex: [ "^[0-9]{9,10}$" ]
        BES.regex: [ "^[356][178][0-9]{5}[1-9][0-9]$" ]
        BLR.regex: [ "^[A-Z]{2}[0-9]{7}$|^[0-9]{9}$" ]
        CAN.regex: [ "^[0-9]{8,9}$|^[0-9]{9}[A-Z]{2}[0-9]{4}$" ]
        CHL.regex: [ "^[0-9]{1,2}\\.[0-9]{3}\\.[0-9]{3}-[0-9K]$" ]
        CHN.regex: [ "^[0-9]{8}[0-9A-HJ-NPQRTUWXY]{10}$|^[0-9]{15}$|^[CWHMT][0-9]{16}[0-9A-Z]$|^J[0-9]{14}$" ]
        COL.regex: [ "^[0-9]{8,10}$" ]
        CRI.regex: [ "^[0-9]{10,11}$|^[0-9]-[0-9]{4}-[0-9]{4}$|^[0-9]-[0-9]{3}-[0-9]{6}$" ]
        HRV.regex: [ "^[0-9]{11}$" ]
        CUW.regex: [ "^1[178][0-9]{5}[1-9][0-9]$" ]
        CYP.regex: [ "^[0-9]{8}[A-Z]$" ]
        CZE.regex: [ "^[0-9]{8,10}$" ]
        DNK.regex: [ "^(((CVR)|(SE))\\s)?[0-9]{8}$|^[0-9]{6}-[0-9]{4}$" ]
        EST.regex: [ "^[189][0-9]{7}$|^[1-6][0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])[0-9]{4}$" ]
        ECU.regex: [ "^[0-9]{13}$" ]
        FRO.regex: [ "^[0-9]*$|^[0-9]{6}-?[0-9]{3}$" ]
        FIN.regex: [ "^[0-9]{6}[-\\+A][0-9]{3}[0-9A-Z]$|^[0-9]{7}-[0-9]$" ]
        FRA.regex: [ "^[0-3][0-9]{12}$|^[0-9]{9}$|^[0-9]{1,5}[A-Z]$|^[0-9]{14}$|^[0-9A-Za-z]{1,11}$" ]
        DEU.regex: [ "^[0-9]{10,12}$" ]
        GIB.regex: [ "^[0-9]{1,6}$" ]
        GRC.regex: [ "^[0-9]{9}$" ]
        GRL.regex: [ "^[0-9]{10}$|^[0-9]{8}$" ]
        GGY.regex: [ "^GY[0-9]{6}$|^[A-Z]{2}[0-9]{6}[A-DFMP]$|^(JY[0-9]{6}[A-Z])$|^[0-9]{1,6}$|^((CH|NP)[0-9]{1,3})$|^[0-9][A-Z]{1,2}[0-9]{6}B{0,1}$" ]
        HKG.regex: [ "^[A-Z]{1,2}[0-9]{6}[0-9A]$|^[0-9]{8}$" ]
        HUN.regex: [ "^[1-8][0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])[0-9]{4}$|^8[0-9]{9}$|^[0-9]{8}-[1-5]-[0-9]{2}$|^[0-9]{8}[1-5][0-9]{2}$" ]
        ISL.regex: [ "^[0-9]{6}-?[0-9]{4}$" ]
        IND.regex: [ "^[A-Z]{3}[PFCAHTBLJG][A-Z][0-9]{4}[A-Z]$" ]
        IMN.regex: [ "^[HCX][0-9]{6}(-[0-9]{2})?$|^[HCX][0-9]{6}-[0-9]{2}Q[0-9]{2}?$|^[A-Z]{2}[0-9]{6}[A-DFMP]$" ]
        IDN.regex: [ "^[0-9]{2}\\.[0-9]{3}\\.[0-9]{3}\\.[0-9]-[0-9]{3}\\.[0-9]{3}$|^[0-9]{15}$" ]
        IRL.regex: [ "^([0-9]{7}[A-Z]{1,2})$|^(CHY\\s[0-9]{1,5})$" ]
        ISR.regex: [ "^[0-9]{9}$" ]
        ITA.regex: [ "^[0-9]{11}$|^[A-Z]{6}[0-9]{2}[A-Z][0-9]{2}[A-Z][0-9]{3}[A-Z]$" ]
        JAM.regex: [ "^[0-9]{9}$" ]
        JPN.regex: [ "^[0-9]{12,13}$" ]
        JEY.regex: [ "^[0-9]{10}$|^[A-Z]{2}[0-9]{6}[A-DFMP]$|^[C-E][A-Z][0-9]{1,5}$" ]
        KWT.regex: [ "^[0-9]*$" ]
        KEN.regex: [ "^[0-9]{11}$" ]
        KGZ.regex: [ "^[1-2](0[1-9]|[12][0-9]|3[01])(0[1-9]|1[0-2])(19[0-9]{2}|20[0-9]{2})[0-9]{6}$|^0(0[1-9]|[12][0-9]|3[01])(0[1-9]|1[0-2])(19[0-9]{2}|20[0-9]{2})[0-9]{5}$" ]
        KOR.regex: [ "^[0-9]{3}-[0-9]{2}-[0-9]{5}$|^[0-9]{6}-[0-9]{7}$" ]
        KAZ.regex: [ "^[0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[1|2][0-9]|3[0|1])[0-9]{6}$|(0[1-9]|1[0-2])[0-9]{2}4[0-2][0-9]{6}" ]
        LVA.regex: [ "^[0-9]{11}$" ]
        LIE.regex: [ "^[0-9]{1,12}$" ]
        LTU.regex: [ "^[0-9]{9,10}$" ]
        LUX.regex: [ "^[0-9]{11}$|^[0-9]{13}$" ]
        MDA.regex: [ "^[1-2][0-9]{12}$" ]
        MAF.regex: [ "^[0-9]{1,5}[A-Z]$|^[0-9]{9}$" ]
        MAC.regex: [ "^[08][0-9]{7}$" ]
        MYS.regex: [ "^([SO]G)\\s[0-9]{1,11}$|^[A-Z]{1,2}\\s[0-9]{1,10}$" ]
        MLT.regex: [ "^[0-9]{3,7}[MGAPLHBZ]$|^[0-9]{9}$" ]
        MHL.regex: [ "^([0-9]{4}-04)$|^(04-[0-9]{4})$|^[0-9]{7,8}$" ]
        MUS.regex: [ "^[12378][0-9]{7}$" ]
        MEX.regex: [ "^[A-Z]{3,4}[0-9]{6}[A-Z0-9]{3}$" ]
        NRU.regex: [ "^[0-9]{2}-[0-9]{5}-[0-9]-[0-9]$" ]
        NLD.regex: [ "^[0-9]{9}$" ]
        NZL.regex: [ "^[0-9]{8,9}$" ]
        NOR.regex: [ "^[89][0-9]{8}([A-Z]{3})?$|^[0-9]{11}$" ]
        NGA.regex: [ "^[0-9]{8}(-[0-9]{4})?$" ]
        PAK.regex: [ "^[0-9]{13}$" ]
        PER.regex: [ "^(10|15|20)[0-9]{9}$|^[0-9]{8}$" ]
        PAN.regex: [ "^[0-9]{5}-[0-9]{2}-[0-9]{6}$|^[0-9]{13}$" ]
        PRY.regex: [ "^[A-Z]{4}-[0-9]{6}[A-Z0-9]$" ]
        PHL.regex: [ "^[0-9]{9}$|^[0-9]{12}$" ]
        POL.regex: [ "^[0-9]{10,11}$" ]
        PRT.regex: [ "^[1-3][0-9]{8}$|^45[0-9]{7}$|^[5-6][0-9]{8}$|^(70|71|72|74|77|78|90|91|92|98|99)[0-9]{7}$" ]
        ROU.regex: [ "^[1-9][0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[1|2][0-9]|3[0|1])(0[1-9]|[1-3][0-9]|4[0-7]|51|52)[0-9]{4}$|^90{3}[0-9]{8}$|^[1-9]\\d{1,9}$" ]
        RUS.regex: [ "^[0-9]{10}$|^[0-9]{12}$" ]
        LCA.regex: [ "^[0-9]{1,6}$" ]
        SMR.regex: [ "^[0-9]{1,9}$|^SM[0-9]{5}$" ]
        SYC.regex: [ "^[0-9]{2}[123457][0-9]{6}$" ]
        SGP.regex: [ "^((([0-9]{4,5})|(F(000|\\s{3})[0-9])|([ST][0-9]{2}[A-Z]{2})|A[0-9]{3})[0-9]{4}[A-Z])$|^[STFG][0-9]{7}[A-Z]$" ]
        SVK.regex: [ "^[0-9]{9,10}$" ]
        SVN.regex: [ "^[1-9][0-9]{7}$" ]
        SXM.regex: [ "^4[178][0-9]{5}[1-9][0-9]$" ]
        ZAF.regex: [ "^[01239][0-9]{3}\\/[0-9]{3}\\/[0-9]{3}$" ]
        ESP.regex: [ "^[0-9LMKXYZ][0-9]{7}[A-Z]$|^[A-HJPQSNUVW][0-9]{7}[0-9A-J]$" ]
        SWE.regex: [ "^(16)\\d{10}$" ]
        CHE.regex: [ "^CHE-[0-9]{3}\\.[0-9]{3}\\.[0-9]{3}$|^756\\.[0-9]{4}\\.[0-9]{4}\\.[0-9]{2}$|^CHE-[0-9]{9}$|^756[0-9]{10}$" ]
        VCT.regex: [ "^[0-9]*$" ]
        SAU.regex: [ "^[0-9]{10}$" ]
        TWN.regex: [ "^[A-Z][0-9]{9}$" ]
        THA.regex: [ "^[0-9]{13}$" ]
        TJK.regex: [ "^[0-9]{9,10}$" ]
        TTO.regex: [ "^[0-9]{9}-[0-9]$" ]
        TUR.regex: [ "^[0-9]{10,11}$" ]
        GBR.regex: [ "^[0-9]{10}$|^(?!BG)(?!GB)(?!NK)(?!KN)(?!TN)(?!NT)(?!ZZ)(?:[A-CEGHJ-PR-TW-Z][A-CEGHJ-NPR-TW-Z])(?:\\s*\\d\\s*){6}([A-DFMP]|\\s)$" ]
        USA.regex: [ "^[A-Z0-9]{6}\\.[A-Z0-9]{5}\\.[A-Z]{2}\\.[0-9]{3}$|^[0-9]{9}$" ]
        VEN.regex: [ "^[A-Z]-[0-9]{8}-[0-9]$" ]
        URY.regex: [ "^[0-9]{8,9}$|^[0-9]{12}$" ]
        UZB.regex: [ "^[2-7][0-9]{8}$" ]

# case-change-analyser-service
sapphirus.diligence.case-change-analyser:
  enable-immediate-processing: true

analyser:
  collection: sph_case-change-analyser_case-status
  artifact-collection: sph_case-change-analyser_data-artifacts
  case-change-task:
    rate: 60000
    max-case-age-in-seconds: 300


# case-sewing-service
case-sewing:
  publishResults: true
  checkIncomingArtifacts: true

# claimcheck-api-service
claim-check:
  expurgation-task:
    rate: 86400
    ttl-seconds: 0

# content-manager-service
sapphirus:
  rootFolderPath: /resources/sapphirus/
  mongo:
    collection: Document

dispatcher:
  user: kpmg-system
  context-collection: dispatcher-context
  request-collection: dispatcher-requests
  controller:
    max-cancel-request-guids: 1000000
    max-republish-request-guids: 1000000

# extraction-builder-key-value-service
sphbuilder.default.document.type: UNKNOWN_DOCUMENT_TYPE

# extraction-classifier-service
sapphirus.extraction.classifier:
  modelId: "GENERAL_CLASSIFICATION_MODEL_v1.2"
  thresholds:
    unknownDocumentType: "UNKNOWN_DOCUMENT_TYPE"
    global: 0.5
    local:
      - document_type: "STATEMENT_INCORPORATION_PT"
        value: 0.70
      - documentType: "UBO"
        value: 0.90
      - documentType: "UBO_LOOKUP"
        value: 0.70
    unknown_content_pages: 0.2
  azure:
    endpoint: https://url.com
    key: key

# extraction-extractor-form-recognizder-service
sapphirus.extraction.extractor:
  work-modes:
    service-requests:
      enabled: false
      service-codes:
        - DOCUMENT_UNSUPERVISED_EXTRACTION
    data-artifacts:
      enabled: true
      artifact-schema-code: DOCUMENT
      qc-status: PASSED

# extraction-manager-service
sapphirus.extraction.manager:
  work-modes:
    service-requests:
      enabled: true
      serviceCodes:
        - EXTRACTION_CODE
    data-artifacts:
      enabled: true
      artifact-schema-code: DOCUMENT
      qc-status: PASSED
    classified-artifacts:
      enabled: true
  verifiable-docs:
    - IDCARD_PT
    - IDCARD_ESP
    - IDCARD_LUX
    - PASSPORT
    - PASSPORT_PT
    - PASSPORT_NON_PT

# extraction-splitter-service
sapphirus.extraction.splitter:
  sequences:
    - documentCombination: UBODECLARATION_AND_LOOKUP_PT, UBODECLARATION_PT
      resultDocumentType: UBODECLARATION_AND_LOOKUP_PT
      ordered: true
    - documentCombination: IDCARD_PT_FRONT, IDCARD_PT_BACK
      resultDocumentType: IDCARD_PT
      ordered: false
      isStandaloneType: false
    - documentCombination: IDCARD_PT_COMPLETE
      resultDocumentType: IDCARD_PT
      ordered: false
      isStandaloneType: false
    - documentCombination: IDCARD_ESP_FRONT, IDCARD_ESP_BACK
      resultDocumentType: IDCARD_ESP
      ordered: false
      isStandaloneType: false
    - documentCombination: IDCARD_ESP_COMPLETE
      resultDocumentType: IDCARD_ESP
      ordered: false
      isStandaloneType: false
    - documentCombination: IDCARD_LUX_FRONT, IDCARD_LUX_BACK
      resultDocumentType: IDCARD_LUX
      ordered: false
      isStandaloneType: false
    - documentCombination: IDCARD_LUX_COMPLETE
      resultDocumentType: IDCARD_LUX
      ordered: false
      isStandaloneType: false
  nonContentPages:
    documentType: UNKNOWN_DOCUMENT_TYPE
    threshold: 0.2

# extraction-storage-service
sapphirus.extraction.storage:
  collection: extracted-contents
  mongo:
    use-grid-fs-storage: false

# Case Storage
sapphirus.case-storage:
  mongo:
    use-grid-fs-storage: false
    auto-index-creation: true
    remove-old-files-after-seconds: 2
  index:
    ontologies:
      ACCOUNT_OPENING_ONTOLOGY:
        parties:
          CORPORATE:
            - name

# extraction-verifier-service
sapphirus.extraction-verifier:
  pending-result-retrieval:
    rate-in-millis: 15000
  regula:
    verifiable-doc-types:
      - IDCARD_ESP
      - IDCARD_LUX
      - PASSPORT
      - PASSPORT_NON_PT
      - PASSPORT_PT
    license: abcdefghijklmnopqrstuvwxyz0123456789
    baseUrl: http://url.com
    authenticationParameters:
      # See more here:
      # https://docs.regulaforensics.com/develop/doc-reader-sdk/web-service/development/usage/authenticity/
      checkLiveness: false
      checkEd: false
      checkHolo: false
      checkMli: false
      checkOvi: false
      checkFibers: true
      checkExtMrz: true
      checkAxial: true
      checkImagePatterns: true
      checkLetterScreen: true
      checkBarcodeFormat: true
      checkExtOcr: true
      checkIpi: true
      checkPhotoComparison: true
      checkPhotoEmbedding: true
      checkIrVisibility: false
      checkIrb900: false
      checkUvLuminescence: false
    docTypeMapper:
      # See com.regula.documentreader.webclient.model.DocumentType for more types:
      # IDENTITY_CARD = 12
      # PASSPORT = 11
      IDCARD_PT: [ 12 ]
      IDCARD_ESP: [ 12 ]
      IDCARD_LUX: [ 12 ]
      PASSPORT: [ 11 ]
      PASSPORT_PT: [ 11 ]
      PASSPORT_NON_PT: [ 11 ]
  au10tix:
    verifiable-doc-types:
      - IDCARD_PT
    toleranceToRisk: medium # Possible values (case-insensitive): low, medium, high, ultrahigh
    doubleCheckDirective: none # Possible values (case-insensitive): none, senddirectly, skipsending
    clientId: abc123
    baseUrl: http://localhost:8080
    accessTokenUrl: http://localhost:8080
    jwtAudience: http://localhost:8080
    publicRsaKeyId: abc123
    privateRsaKey: abcdefghijklmnopqrstuvwxyz0123456789
    startIdVerificationWorkflowUrl: /foo/bar
    getAggregatedResultsBaseUrl: /foo/bar

# party-change-analyser-service
sapphirus.party-change-analyser:
  party-change-task:
    rate: 60000
    max-age-in-seconds: 300

  # policy-service
  # Policy Service
policy-service:
  use-grid-fs-storage: false

sphconfig:
  collections:
    artifact-schemas: sph_sphconfig_artifact-schemas
    ontologies: sph_sphconfig_ontologies
    ontology-versions: sph_sphconfig_ontology_versions
    global-enums: sph_sphconfig_global-enums
    database-sequence: sph_sphconfig_ontology_database-sequences
hash.validator.status: false

# recycling-service
recycler:
  recycleRequester:
    serviceCodes:
      - RECYCLE_DOCS

# timeout-controller-service
sapphirus.diligence.timeout-controller:
  collection: sph_timeout-controller
  scheduler-task-rate-ms: 1000

party-manager:
  mongo:
    use-grid-fs-storage: false
  graph:
    default:
      enable: false
    neo4j:
      enable: true

# fragment-stylist-service properties
sph.fragment-stylist:
  greedy: false
  useEmptyAttributeValues: false