<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>pt.kpmg.sapphirus</groupId>
        <artifactId>sapphirus-services</artifactId>
        <version>1.3.37-SNAPSHOT</version>
        <relativePath>../sapphirus-services/pom.xml</relativePath>
    </parent>

    <artifactId>sapphirus-tests</artifactId>

    <dependencies>
        <!-- SPH MODULES TO TEST -->

        <!-- Configuration Manager -->
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>configuration-manager-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>configuration-manager-connector-jpa</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>configuration-manager-connector-mysql</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>configuration-manager-connector-storage-fileshare</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>configuration-manager-connector-rest-api</artifactId>
            <scope>test</scope>
        </dependency>


        <!-- Artifact Quality -->
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>artifact-quality-service</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>artifact-quality-connector-rest</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>artifact-quality-client-rest</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>artifact-quality-connector-kafka</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Management Information -->
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>management-information-connector-mysql</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>management-information-connector-kafka</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>management-information-service</artifactId>
            <scope>test</scope>
        </dependency>
        <!--AUDIT -->
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>audit-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>audit-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>audit-connector-mongodb</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>audit-connector-rest</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Others -->
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>timeout-controller-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>extraction-storage-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>case-sewing-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>case-change-analyser-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>form-recognizer-extraction-extractor-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>content-manager-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>policy-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>case-stylist-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>artifact-api-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>artifact-api-client</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>extraction-manager-service</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Case Storage -->
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>case-storage-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>case-storage-connector-storage-mongodb</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>case-storage-connector-rest</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>case-storage-connector-graphql</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>case-storage-connector-kafka</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>case-storage-client-rest</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>dispatcher-starter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>key-value-artifact-builder-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>case-review-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>fragment-stylist-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>party-manager-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>party-manager-graph-connector-neo4j</artifactId>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>extraction-splitter-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>claimcheck-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>extraction-classifier-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>recycling-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>party-change-analyser-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>party-dispatcher-starter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>extraction-verifier-service</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Tenant Management -->
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>tenant-management-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>tenant-management-connector-jpa-mysql</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>tenant-management-connector-rest</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>tenant-management-connector-kafka</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>tenant-management-client-rest</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- SPH LIBRARIES -->
        <dependency>
            <groupId>pt.kpmg.sapphirus.libraries</groupId>
            <artifactId>entities</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.libraries</groupId>
            <artifactId>utils</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.libraries</groupId>
            <artifactId>starter-multitenancy</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- COMMON STARTERS -->
        <dependency>
            <groupId>pt.kpmg.starters.common</groupId>
            <artifactId>kafka-starter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.starters.common</groupId>
            <artifactId>jpa-starter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.starters.common</groupId>
            <artifactId>flyway-starter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.starters.common</groupId>
            <artifactId>rest-starter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.starters.common</groupId>
            <artifactId>error-starter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.starters.common</groupId>
            <artifactId>mongodb-starter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.starters.common</groupId>
            <artifactId>oauth2-server-starter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.starters.common</groupId>
            <artifactId>oauth2-client-starter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.starters.common</groupId>
            <artifactId>cache-starter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.starters.common</groupId>
            <artifactId>monitor-starter</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- TEST DEPENDENCIES -->
        <dependency>
            <groupId>pt.kpmg.sapphirus.libraries</groupId>
            <artifactId>starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.libraries</groupId>
            <artifactId>starter-lanes</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>de.flapdoodle.embed</groupId>
            <artifactId>de.flapdoodle.embed.mongo.spring3x</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-resource-server</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>it.ozimov</groupId>
            <artifactId>embedded-redis</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.neo4j.test</groupId>
            <artifactId>neo4j-harness</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- This dependency solves a dependency conflict between Neo4j Harness and Spring Data JPA. -->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>audit-connector-kafka</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>pt.kpmg.sapphirus.services</groupId>
            <artifactId>case-storage-connector-index-mongodb</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>